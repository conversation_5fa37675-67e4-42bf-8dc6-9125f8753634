import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { Location } from '@angular/common';
import { AlertController, Platform, PopoverController, ModalController, NavController } from '@ionic/angular';
import { File as NativeFile } from '@awesome-cordova-plugins/file/ngx'; // https://forum.ionicframework.com/t/name-conflict-between-cordova-plugin-file-and-javascript-file-interface/185290
import { ToastrService } from 'ngx-toastr';
import * as $ from 'jquery';
import * as hash from 'object-hash';

import { UnviredCordovaSDK, RequestType, SyncResult, DbResult, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { TASK_SUBMISSION_HEADER, TASK_SUBMISSION_ATTACHMENT, FORM_HEADER, TASK_USER_HEADER, TASK_HEADER, PRIORITY_HEADER, FORM_EVE_ANALYTICS_ITEM, FORM_EVE_ANALYTICS_HEADER } from 'src/app/services/HEADER';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
// import { TaskAlertPage } from '../task-alert/task-alert.page';
// import { FormDocumentsPage } from './../form-documents/form-documents.page';
import { TranslateService } from '@ngx-translate/core';
import { OptionsPopoverPage } from '../options-popover/options-popover.page';
// import { FormStatusPage } from "../form-status/form-status.page";
import { sha256 } from "js-sha256";
import { ImagePreviewPage } from '../image-preview/image-preview.page';
import { Geolocation } from '@awesome-cordova-plugins/geolocation/ngx';
import { AndroidPermissions } from '@awesome-cordova-plugins/android-permissions/ngx';
import { AppConstants } from 'src/app/shared/app-constants';
import { DataService } from 'src/app/services/data.service';
import { ShareDataService } from 'src/app/services/ShareData.service';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';
import { HelperFunctionService } from 'src/app/services/HelperFunction.service';
import { AppSpecificUtilityService } from 'src/app/services/app-specific-utility.service';
import { NetworkConnectionService } from 'src/app/services/network-connection.service';
import { PERMIT_FORM, PERMIT_HEADER } from 'src/app/data-models/data_classes';
import BMF from 'browser-md5-file';
import download from 'downloadjs';

declare const loadForm: any;
declare const submit: any;
declare const wizardSubmit: any;
declare const returnTempData: any;
declare const returnLastTempData: any;
declare const setLocation:any;
let that: any;

/**
 * Gauge Chart is included in the index.html file as a script.
 * We are declaring a variable here to mimic the exported variable from GaugeChart.
 * More info can be found here: https://github.com/recogizer/gauge-chart#readme
 */
declare const GaugeChart: any;

@Component({
  selector: 'app-form-render',
  templateUrl: './form-render.page.html',
  styleUrls: ['./form-render.page.scss'],
})
export class FormRenderPage implements OnInit {

  // constructor() { }

  // ngOnInit() {
  //   // $('head').append('<link rel="stylesheet" href="assets/js/formio/formio.full.min.css" type="text/css" />');
  //   // $('head').append('<link rel="stylesheet" href="assets/css/responsive-grid.min.css" type="text/css" />');

  //   let form = '{"formId":"31BDE8FC7E1E4885A9AE2FFC8203D877","title":"Save And Complete Test","description":"Save And Complete Test","display":"form","type":"form","name":"saveandcompletetest","path":"saveandcompletetest","tags":"Demo,UNVIRED","avatar":"description","formtype":"form","usage":"masterdata","localExecute":false,"components":[{"html":"<p>This is a form for testing Save and Complete Message:</p><ol><li>Save<ol><li>There are 2 direct Mandatory fields</li><li>If the Checkbox AddField is checked, a new field is shown which is made mandatory</li></ol></li><li>Complete<ol><li>Depends on the mandatory fields above</li><li>If Name field (not mandatory) is filled in only then it can be completed as it controls the complete check</li></ol></li></ol>","label":"Help","refreshOnChange":false,"key":"help","type":"content","input":false,"tableView":false,"hidden":false},{"label":"Text Field","tableView":true,"validate":{"required":true},"key":"textField","type":"textfield","input":true,"hidden":false},{"label":"Number","mask":false,"tableView":false,"delimiter":false,"requireDecimal":false,"inputFormat":"plain","validate":{"required":true},"key":"number","type":"number","input":true,"hidden":false},{"label":"Name","placeholder":"Print your name to complete form","tableView":true,"case":"uppercase","key":"name","type":"textfield","input":true,"hidden":false},{"label":"Complete Control","hidden":true,"tableView":false,"defaultValue":false,"clearOnHide":false,"calculateValue":"value = data.name.length > 0;","key":"completeControl","type":"checkbox","input":true},{"label":"Add Field","tableView":false,"key":"addField","type":"checkbox","input":true,"defaultValue":false,"hidden":false},{"label":"New Field","tableView":true,"redrawOn":"addField","clearOnHide":false,"key":"newField","logic":[{"name":"Hide","trigger":{"type":"simple","simple":{"show":true,"when":"addField","eq":"false"}},"actions":[{"name":"Hide Field","type":"property","property":{"label":"Hidden","value":"hidden","type":"boolean"},"state":true},{"name":"Not needed","type":"property","property":{"label":"Required","value":"validate.required","type":"boolean"},"state":false}]},{"name":"Show Mandatory","trigger":{"type":"simple","simple":{"show":true,"when":"addField","eq":"true"}},"actions":[{"name":"Show It","type":"property","property":{"label":"Hidden","value":"hidden","type":"boolean"},"state":false},{"name":"Need It","type":"property","property":{"label":"Required","value":"validate.required","type":"boolean"},"state":true}]}],"type":"textfield","input":true,"hidden":false},{"input":true,"tableView":false,"label":"Submit","key":"submit","clearOnHide":false,"spellcheck":false,"type":"button","rows":0,"wysiwyg":false,"lockKey":true,"hidden":false}]}';
  //   let submissionData = '{"name":"","number":3,"submit":false,"addField":true,"newField":"shyamala","textField":"test","completeControl":false}';
  //   loadForm(JSON.parse(form), JSON.parse(submissionData), false);
  // }

  public constants: AppConstants;
  public devicePlatform: string = '';
  public isHybridNative: boolean;
  public formReadOnlyFlag: boolean = false;
  public fileUploadFlag: boolean = false;
  public completeFlag: boolean = false;
  public markComplete: boolean = false;
  public completeIconFlag: boolean = false;
  public headerOpen: boolean = false;
  public flagToLoadFormOrFormset: string = '';
  public formDescription: string = '';
  public formData: any;
  public formSubmissionData: any;
  public masterData: any;
  public subData: any;
  public fileArray: any[] = [];
  public fileAttachmentMap: any = {};
  public attachments: any[] = [];
  public uploadedFiles: any[] = [];
  public deletedArray: any[] = [];
  public addedArray: any[] = [];
  public locationPath: string = '';
  public cssSettings: any = [];
  public lastFormData: any;
  public task: TASK_HEADER;
  public taskUser: TASK_USER_HEADER;
  public form: FORM_HEADER;
  public taskSubmission: TASK_SUBMISSION_HEADER;
  public lastSubmissionTimestamp: number;
  public formInitializedWith: string;
  // public updatedTaskSubmission = {} as TASK_SUBMISSION_HEADER;
  public permitFormSubmissionData = {} as PERMIT_FORM;
  public permitHeaderData = {} as PERMIT_HEADER;
  public attachmentDbResult: TASK_SUBMISSION_ATTACHMENT[];
  public isTaskTypeShift: boolean = false;
  public showMobileView: boolean = false;
  public showAlertBadge: boolean = false;
  public showFormDocs: boolean = false;
  public alertCount: number = 0;
  public unReadCount: number = 0;
  public formFileCompArray: string[] = [];
  public setBarcode: any = false;
  public componentObj: any;
  public translateJson: any;
  public attributesJson: any;
  public tabProperties: any;
  public componentID: string = '';
  public FirstTimeFlag: boolean = false;
  public formDataRestore: any;
  public cameraOpen: boolean = false;
  public filetasksubmission: any;
  public formFileCompObj = [];
  public backbuttonEventListened: boolean = false;
  public completion: boolean = false;
  public calculatedPercentage = 0;
  public subErr: string;
  private lastTimeBackPress = 0;
  private timePeriodToExit = 2000;
  public showChart: boolean = false;
  public analyticsItem: FORM_EVE_ANALYTICS_ITEM;
  public analyticHeader: FORM_EVE_ANALYTICS_HEADER;
  public seqEve: number = 0;
  public additionalDataEve: string = "";
  public showReviewComments: boolean = false;
  public showUnreadAlerts: boolean = false;
  public annotationEnd: boolean = false;
  public createFlag: boolean = false;
  public formViewOnlyFlag: boolean = false;
  public formReadWriteFlag: boolean = false;
  public isRequestApproval: boolean = false;
  public isSubmitFormViaActionButton: boolean = false;
  public isAndroid: boolean = false;
  public renderType: string = "";
  public buttonType: string;
  public tempData: any;
  public hash: any;
  public UID: any;
  public isFormLoaded: boolean = false;
  public currentUserDetails: any;
  public isPrimaryUserCompleted: boolean = false;
  public imagePreviewModalOpened:  boolean = false;
  public isdisableFormCompletion:  boolean = false;
  public locationComponentID: string = '';

  constructor(
    private location: Location,
    private platform: Platform,
    private route: ActivatedRoute,
    private file: NativeFile,
    private toastr: ToastrService,
    private alertController: AlertController,
    private modalController: ModalController,
    private popoverController: PopoverController,
    private unviredSDK: UnviredCordovaSDK,
    private fetchData: DataService,
    private shareData: ShareDataService,
    private loader: BusyIndicatorService,
    private utilityFunction: HelperFunctionService,
    private appSpecificUtility: AppSpecificUtilityService,
    private iab: InAppBrowser,
    private translate: TranslateService,
    public popoverCtrl: PopoverController,
    private router: Router,
    private networkConection: NetworkConnectionService,
    private geolocation: Geolocation,
    private andoridPermissions: AndroidPermissions
  ) {
    this.constants = new AppConstants();
    this.FirstTimeFlag = false;
    
  }

  async ngOnInit() {
    // await this.setFormInitializedData();
    console.log("form- render : ngOnInit called")
  }
  toggleIcon() {
    this.headerOpen = !this.headerOpen;
  }

  async getCapturedCordinates() {
    return await this.appSpecificUtility.getLocationCoordinates();
  }

  async ionViewWillEnter() {
    console.log("ionViewWillEnter called");
    this.shareData.setImagePreviewModalOpened(false)

    that = this;
    this.devicePlatform = this.shareData.getDevicePlatform();
    this.devicePlatform = (this.devicePlatform == '' || this.devicePlatform == undefined) ? 'browser' : this.devicePlatform;
    this.isHybridNative = (this.devicePlatform === 'browser') ? false : true;
    this.route.queryParams.subscribe(params => {
      this.flagToLoadFormOrFormset = params['type'];
      this.renderType = params['viewType'];
    });
    if (!this.flagToLoadFormOrFormset) {
      this.flagToLoadFormOrFormset = 'form';
    }
    this.route.queryParams.subscribe(params => {
      this.createFlag = params['createFlag'];
    })
    this.appSpecificUtility.getViewMode().subscribe((mobileView: boolean) => {
      this.showMobileView = mobileView;
    });

    // After successfull scan |SCANNER RETURNED DATA| 
    let component = this;
    this.shareData.getBackToRenderer.subscribe((data) => {
      component.setBarcode = false;
      setTimeout(async () => {
        component.ionViewDidEnter();
        if (data !== '') {
          this.shareData.setNavigateFromCameraPage(true);
          // fire the scanned data event
          let eventCustom = new CustomEvent('barcodeScannedData', {
            detail: {
              componentID: component.componentID,
              scannedData: data
            }
          });

          document.dispatchEvent(eventCustom);
        } else {
          let barcodeChange = new CustomEvent('barcodeChangeEvent', {
            detail: {}
          });
          document.dispatchEvent(barcodeChange);
        }
      }, 800);
    });

    // listener for the form mandatory fields completion percentage alert
    document.addEventListener('BtnAction', async (event: any) => {
      this.completion = false;
      let previousVal = this.calculatedPercentage;
      // localStorage.setItem('eventPer', event.detail.calculationPercentage);
      await this.setButtonType(event.detail.tempData, event.detail.calculationPercentage);

      this.calculatedPercentage = Math.round(event.detail.calculationPercentage);
      if (event.detail.calculationPercentage == 100 && event.detail.errObj.length == 0) {
        this.completion = true;
        this.subErr = '';
      } else if (event.detail.errObj.length > 0) {
        this.subErr = 'X';
      }
      let ele = document.getElementById('badge');
      var gradColor1 = `${"#90EE90"} ${this.calculatedPercentage}%`;
      var gradColor2 = `${"#ffffff"} ${100 - this.calculatedPercentage}%`;
      if (this.calculatedPercentage == 0) {
        if(ele && ele.style)
        ele.style.background = "#ffffff";
      } else {
        if(ele && ele.style)
        ele.style.background = `linear-gradient(to right, ${gradColor1}, ${gradColor2})`;
      }
      // update on every change
      // this.initialisePercentageLoader(previousVal);
      // this.setButtonType();
    });

    document.addEventListener('click', () => {
      setTimeout(() => {
        let ele = document.querySelector('.renderOptions .popover-content');
        if (ele && ele.getAttribute('id')) {
          let eleWidth = parseFloat(getComputedStyle(ele).width);
          let top = parseFloat(getComputedStyle(ele).top);;
          ele.removeAttribute('id')
          if (window.innerWidth) {
            ele.setAttribute('style', `left: ${window.innerWidth - eleWidth}px !important; top: ${top}px !important; transform-origin: right top;`)
          }
        }
      }, 100);
    });
   
    this.platform.ready().then(() => {
      this.platform.backButton.subscribeWithPriority(0, () => {
        if (new Date().getTime() - this.lastTimeBackPress >= this.timePeriodToExit) {
          this.lastTimeBackPress = new Date().getTime();
          if (this.router.url == "/settings") {
            // Don't check for go back 
            this.router.navigate(['/permits'])
          } else {
            this.goBackToLastLocation();
          }
        } else {
          // Ignore. This implies multiple events in quick succession.
          // This is to account for false events. 
        }
      });
    });
    this.headerOpen = false;
    if (this.loader.isLoading) {
      this.unviredSDK.logInfo('FormRendererPage', 'ionViewWilEnter()', 'Hiding Busy indicator...')
      await this.loader.dismissBusyIndicator();
    }
    if (this.devicePlatform != '' && !this.loader.isLoading) {
      await this.loader.showBusyIndicator(this.translate.instant('Loading form...'), 'crescent');
    }
    let page = document.querySelector('app-form-renderer');
    if (page) {
      page.setAttribute('id', 'render-page');
    }
    let _self = this;
    document.addEventListener('getLocationFromApp', async function(event: any){
      _self.locationComponentID = event.detail.controlId;
      console.log("getLocationFromApp event listening");
      // browser
      if (_self.devicePlatform === 'browser') {
        let location = await _self.appSpecificUtility.getLocationCoordinates();
        setLocation(location,  _self.locationComponentID);
      } else {
        // mobile
        let opt = { enableHighAccuracy: true };

        await _self.geolocation.getCurrentPosition(opt).then((resp) => {
          console.log('opt');
          console.log(resp);
          if (resp && resp.coords) {
            let locationParam: string = '';
            locationParam = `${resp.coords.latitude.toString()}, ${resp.coords.longitude.toString()}`;
            setLocation(locationParam,  _self.locationComponentID);
          }
        }).catch((err) => {
          console.log('ERROR!');
          console.log(err);
        });
      }
    });
  }

  async ionViewDidEnter() {
    console.log("ionViewDidEnter called");
    // Adding Form Event listeners    
    document.addEventListener('onFormOpen', this.onFormOpenListener);
    document.addEventListener('onFocus', this.onFocusListener);
    document.addEventListener('onBlur', this.onBlurListener);
    document.addEventListener('AnnotationEnd', this.onAnnotateEndListener);
    document.addEventListener('onChange', this.onChangeListener);
    document.addEventListener('onError', this.onErrorListener);
    this.createFormAndSetFormData();
    document.addEventListener('submitFormViaActionButton', this.submitFormViaActionButtonListener, true);

  }

  async createFormAndSetFormData() {
    $('head').append('<link rel="stylesheet" href="assets/js/formio/formio.full.min.css" type="text/css" />');
    $('head').append('<link rel="stylesheet" href="assets/css/responsive-grid.min.css" type="text/css" />');

    await this.applyCssStyleFromFormIO();
    await this.prepareFormResourcesAndSetFormData();
    await this.prepareFormUploadsAndSubmissionData();
    let loggedInUserIds = await this.appSpecificUtility.getTeamsFromDB();
    // Check task type and form mode |read, edit|
    if(this.task && this.task.TASK_SOURCE)
    this.isTaskTypeShift = (this.task.TASK_SOURCE === this.constants.TASK_SOURCE.SHIFT) ? true : false;
    let formIsValidAndReadyToEdit: boolean = await this.appSpecificUtility.checkFormIsValidAndNotProccessingAnyBE(this.task, this.taskUser, this.taskSubmission, this.devicePlatform, this.loader.isLoading);
    this.formReadOnlyFlag = !formIsValidAndReadyToEdit;

    this.FirstTimeFlag = this.shareData.getNavigateFromCameraPage()
    if (!this.FirstTimeFlag) {
    // Calling Creating form function.
    this.FirstTimeFlag = true;
    let userInfo: any = null;
    let company = this.fetchData.getCompany();
    this.appSpecificUtility.getLoggedInUserInfo().subscribe((data) => {
      userInfo = data[0];
      if (userInfo && Object.keys(userInfo).length === 0 && userInfo.constructor === Object) {

      } else {
        this.currentUserDetails = userInfo;
      }
    });
    this.isAndroid = this.platform.is('android');
    let iosPlatform = this.platform.is('ios')
    setTimeout(() => { 
      if(this.formData && this.formData.components){
      let components = this.formData.components;
      for (let i = 0; i < components.length; i++) {
        if (components[i].type === "form" && components[i].tags && components[i].tags.length > 0) {
          for (let j = 0; j < components[i].tags.length; j++) {
            if (components[i].tags[j] === "FormViewOnly") {
              this.formViewOnlyFlag = true;
            } else if (components[i].tags[j] === 'FormReadWrite') {
              this.formReadWriteFlag = true;
            }
          }
        }
      }

      if (!this.formReadWriteFlag) {
        this.formViewOnlyFlag = true;
      }
      if (this.taskUser && this.taskUser.STATUS) {

        for (let i = 0; i < components.length; i++) {
          if (components[i].type === "form" && components[i].tags && components[i].tags.length > 0) {
            for (let j = 0; j < components[i].tags.length; j++) {
              if (components[i].tags[j] === "Approval") {
                if (this.taskUser.STATUS === this.constants.TASK_USER_STATUS.IN_PROGRESS) {
                  components[i].hidden = true;
                } else if (this.taskUser.STATUS === this.constants.TASK_USER_STATUS.IN_REQUEST) {
                  components[i].disabled = false;
                  // actionButtonColumns
                  if (components[i].formDesign) {
                    let comps = components[i].formDesign.components;
                    for (let j = 0; j < comps.length; j++) {
                      if (comps[j].type === "columns" && comps[j].key === "actionButtonColumns") {
                        comps[j].hidden = true;
                      }
                    }

                  }
                } else if (this.taskUser.STATUS === this.constants.TASK_USER_STATUS.IN_APPROVAL) {
                  components[i].hidden = false;
                  components[i].disabled = false;
                }

                if (this.subData) {
                  let data = this.subData.approval ? this.subData.approval.data : '';
                  if (data && data['approvers'] && data['approvers'].length === 0) {
                    components[i].hidden = true;
                  }
                }
              } else {
                // if (!components[i].disabled && !this.formReadWriteFlag) {
                //   components[i].disabled = true;
                // }
              }
            }
          } else {
            // conditions for normal form
            if (this.taskUser.STATUS === this.constants.TASK_USER_STATUS.IN_PROGRESS) {
              if (!components[i].hidden) {
                components[i].hidden = false;
              }

            } else if (this.taskUser.STATUS === this.constants.TASK_USER_STATUS.IN_REQUEST) {
              if (!components[i].disabled) {
                components[i].disabled = true;
              }
            } else if (this.taskUser.STATUS === this.constants.TASK_USER_STATUS.IN_APPROVAL) {
              // if (!components[i].disabled) {
              //   components[i].disabled = true;
              // }

              if (this.formReadWriteFlag) {
                if (!components[i].disabled) {
                  components[i].disabled = false;
                }
              } else {
                if (!components[i].disabled) {
                  components[i].disabled = true;
                }
              }

            }

          }

          if (this.renderType == "print" || this.taskUser.STATUS === this.constants.TASK_USER_STATUS.APPROVED) {
            this.formReadOnlyFlag = true;
          }
        }
      }
    }
      this.shareData.setFormDataForAnnotation(this.formData);
      this.unviredSDK.logDebug('FormRendererPage', 'ionViewDidEnter()', 'submission data = ' + JSON.stringify(this.subData));
      this.isFormLoaded = false;
      loadForm(this.formData, this.subData, this.formReadOnlyFlag, this.translateJson, userInfo, company, this.isAndroid, iosPlatform, !this.isHybridNative, this.renderType, this.attributesJson, loggedInUserIds, BMF, download);
    }, 100);
    this.shareData.setFirstTimeFlag(true);
    } else {
      if (this.loader.isLoading) {
        await this.loader.dismissBusyIndicator();
      }
    }

    /**
     * Wait for Form to render and then add CSS to make the form responsive.
     * FIXME: Find a way to remove this eventlistener. Because, everytime this page is visited, eventlistener is getting added.
     */
    function formRenderingCompleteHandler(this: any, event) {
      this.unviredSDK.logDebug('FormRendererPage', 'formRenderingCompleteHandler()', 'Form Rendering is complete. Loading CSS..')
      if (!this.isFormLoaded) {
        console.log("calling prepareTabulatorData")
        this.prepareTabulatorData();
        this.isFormLoaded = true;
      }
      this.unviredSDK.logDebug('FormRendererPage', 'formRenderingCompleteHandler()', 'Form Rendering is complete. Loading CSS Complete.')
    }
    function formRenderingCompleteFromapprovalHandler(this: any, event) {
      console.log("formRenderingCompleteFromapprovalHandler called");
      this.prepareTabulatorData();
    }
    document.addEventListener('FormRenderingComplete', formRenderingCompleteHandler.bind(this), false)

    document.addEventListener('FormRenderingCompleteFromapproval', formRenderingCompleteFromapprovalHandler.bind(this), false)

    await this.fetchReviewComments();
    await this.fetchFormUnReadAlertCount();

    // Get alert count and show the alerts
    let formAlertCount = await this.fetchFormAlertCount();
    this.showAlertBadge = (formAlertCount > 0) ? true : false;

    // set form documents flag
    // if(this.form && this.form.DOCUMENTS){
    // this.showFormDocs = (this.form.DOCUMENTS === '' || this.form.DOCUMENTS === null || this.form.DOCUMENTS === undefined) ? false : true;
    // }
    let component = this;
    setTimeout(async function () {
      document.addEventListener('openBarcode', function (event: any) {
        component.componentObj = event.detail.compObj;
        component.setBarcode = true;
        component.componentID = event.detail.controlId;
      }, false);
    }, 200);

    document.addEventListener('smartDataEvent', async function (event: any) {
      const data = event.detail;
      component.subData = (component.subData === '' || component.subData === undefined || component.subData === null) ? {} : component.subData;
      component.subData = data;
      component.taskSubmission.FORM_DATA = JSON.stringify(component.subData);
    }, false);

    let that = this;
    setTimeout(async function () {
      document.addEventListener('openCamera', function (event: any) {
        that.cameraOpen = true;
        const dataToSend: NavigationExtras = { queryParams: { id: event.detail.controlId } };
        that.router.navigate(['camera'], dataToSend);
      }, false);
    }, 200);
    document.addEventListener('displaySDCWorkflowError', this.displaySDCWorkflowErrorHandler.bind(this), true)

    // document.addEventListener('openImagePreview', function (event: any) {
    //  that.openImagePreview(event.detail.data)
    // }, true);
    document.addEventListener('openImagePreview', this.openImagePreviewHandler.bind(this), true)

    
  }

  openImagePreviewHandler(event){
    let imagePreviewModalOpened = this.shareData.getImagePreviewModalOpened()
    if(!imagePreviewModalOpened){
    // if(!this.imagePreviewModalOpened){
    // this.imagePreviewModalOpened = true
    this.shareData.setImagePreviewModalOpened(true)
    this.openImagePreview(event.detail.data)
    }

  }

  async setButtonType(val, calculatedPercentage) {
    
    let disableFormCompletion = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "disable-form-completion" }) : [];
    if (disableFormCompletion && disableFormCompletion.length > 0 && disableFormCompletion[0]) {
      this.isdisableFormCompletion = disableFormCompletion[0].value;
    }else{
      this.isdisableFormCompletion = false;
    }
    console.log("isdisableFormCompletion " + this.isdisableFormCompletion);

    // let calculatedPercentage = Math.round(Number(localStorage.getItem('eventPer')));
    calculatedPercentage = Math.round(Number(calculatedPercentage));
    // let val = await returnTempData();
    this.tempData = val.tempData ? val.tempData : val.submission;
    let completeFld: boolean = true;
    if (calculatedPercentage === 100) {
      let clientCompField = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "client-complete-check-field" }) : [];
      if (clientCompField.length > 0 && this.tempData && this.tempData.data && clientCompField[0].value !== "") {
        completeFld = this.tempData.data[clientCompField[0].value];
      }else{
       if(clientCompField.length > 0 && clientCompField[0].value !== ""){
        completeFld = false;
       }
      }
    }
    if (this.loader.isLoading && this.loader.message == this.translate.instant('Loading form...')) {
      await this.loader.dismissBusyIndicator();
    }
    if (this.renderType == 'print') {
      this.buttonType = this.translate.instant("Print");
    } else {
      this.formReadOnlyFlag = false;
      if (!this.isdisableFormCompletion && (!this.formReadOnlyFlag && (calculatedPercentage === 100 && completeFld))) {
        this.buttonType = this.translate.instant("Complete");
      } else if ((((this.formReadOnlyFlag) || (this.formViewOnlyFlag))) && (this.taskUser && this.taskUser.STATUS !== 'IN PROGRESS')) {
        this.buttonType = this.translate.instant("History");
      } else if (!this.isdisableFormCompletion && (!this.formReadOnlyFlag && calculatedPercentage !== 100 || (calculatedPercentage === 100 && !completeFld)) || ((
        (this.formReadWriteFlag && calculatedPercentage !== 100) || calculatedPercentage !== 100 || (this.formReadWriteFlag && calculatedPercentage === 100 )))) {
        this.buttonType = this.translate.instant("Save");
      } 
      // else if (calculatedPercentage === 100) {
      //   this.buttonType = this.translate.instant("Request Approval");
      // }
      // if (!this.isdisableFormCompletion && (!this.formReadOnlyFlag && (this.task && this.task.TASK_TYPE !== 'APPROVAL') && (calculatedPercentage === 100 && completeFld))) {
      //   this.buttonType = this.translate.instant("Complete");
      // } else if ((((this.formReadOnlyFlag && (this.task && this.task.TASK_TYPE !== 'APPROVAL')) || (this.formViewOnlyFlag && (this.task && this.task.TASK_TYPE === 'APPROVAL')))) && (this.taskUser && this.taskUser.STATUS !== 'IN PROGRESS')) {
      //   this.buttonType = this.translate.instant("History");
      // } else if (!this.isdisableFormCompletion && (!this.formReadOnlyFlag && (this.task && this.task.TASK_TYPE !== 'APPROVAL') && calculatedPercentage !== 100 || (calculatedPercentage === 100 && !completeFld && this.task.TASK_TYPE !== 'APPROVAL')) || ((
      //   (this.formReadWriteFlag && calculatedPercentage !== 100) || calculatedPercentage !== 100 || (this.formReadWriteFlag && calculatedPercentage === 100 && (this.taskUser && this.taskUser.STATUS !== 'IN PROGRESS'))) && (this.task && this.task.TASK_TYPE === 'APPROVAL'))) {
      //   this.buttonType = this.translate.instant("Save");
      // } else if ((this.task && this.task.TASK_TYPE === 'APPROVAL') && (this.taskUser && this.taskUser.STATUS === 'IN PROGRESS') && calculatedPercentage === 100) {
      //   this.buttonType = this.translate.instant("Request Approval");
      // }
    }
  }

  renderBtnClicked(type: string, task: any) {
    switch (type) {
      case this.translate.instant("Print"):
        // this.printPerview();
        break;
      case this.translate.instant("Save"):
        this.markComplete = false;
        this.saveForm();
        break;
      case this.translate.instant("Complete"):
        this.completeFlagChange();
        break;
      case this.translate.instant("History"):
        // this.openFormStatus(task);
        break;
      case this.translate.instant("Request Approval"):
        this.saveFormRequestApproval();
        break;
    }
  }

  async applyCssStyleFromFormIO() {
    // let settingsResult = {} as DbResult;
    // settingsResult = await this.unviredSDK.dbSelect(this.constants.SETTINGS_TABLE, "");
    // if (settingsResult.type === ResultType.success) {
    //   if (settingsResult.data && settingsResult.data.length > 0) {
    //     this.cssSettings = settingsResult.data.filter((element: any) => element.SETTINGS_KEY == "CSS");
    //     if (this.cssSettings.length > 0 && this.cssSettings[0].SETTINGS_VALUE != "") {
          // if (!this.platform.is('desktop') && this.platform.is('mobile')) {
          //   if (((this.platform.is('iphone')) || (this.platform.is('android'))) && (this.devicePlatform !== 'browser')) {
          //     $('head').append('<style class="customCss">' + this.utilityFunction.decodeUnicode(this.cssSettings[0].SETTINGS_VALUE) + '</style>');
          //   } else if (((this.platform.is('iphone')) || (this.platform.is('android'))) && (this.devicePlatform === 'browser')) {
          //     $('head').append('<link href=' + this.cssSettings[0].SETTINGS_VALUE + ' rel="stylesheet"/>');
          //   }
          // } else {
          //   $('head').append('<link href=' + this.cssSettings[0].SETTINGS_VALUE + ' rel="stylesheet"/>');
          // }
        // }
    //   }
    // }
  }

  async prepareFormResourcesAndSetFormData() {
    // Get FORM DATA from last page |FORM, FORMSET|
    let data = this.shareData.getFormData();
    // On reload data would be undifined so take care of RELOAD scenario
    if (data === undefined || Object.keys(data).length == 0) {
      let FORM_IO_ENTITY = localStorage.getItem('FORM_IO_ENTITY');
      data = (FORM_IO_ENTITY != '' && FORM_IO_ENTITY != undefined) ? JSON.parse(FORM_IO_ENTITY) : '';
    }
    console.log("data in form -render")
    this.task = data.task;
    this.taskUser = data.taskUser;
    this.form = data.form;

    if (this.filetasksubmission) {
      this.taskSubmission.FORM_DATA = JSON.stringify(this.filetasksubmission);
      this.filetasksubmission = '';
    } else {
      this.taskSubmission = data.taskSubmission;
    }
    if (this.form && this.form.FORM_DESC) {
      this.formDescription = this.form.FORM_DESC;
    }
    let formDesign = this.utilityFunction.decodeUnicode(this.form ? this.form.TEMPLATE : "");
    let formIOComponent = (formDesign === '') ? '' : JSON.parse(formDesign);
    // if (this.form && this.form.TRANSLATION) {
    //   this.translateJson = JSON.parse(this.utilityFunction.decodeUnicode(this.form? this.form.TRANSLATION:""));
    // }
    // if (this.form && this.form.ATTRIBUTES) {
    //   this.attributesJson = JSON.parse(this.form.ATTRIBUTES);
    // }
    if(this.taskSubmission && this.taskSubmission.FORM_DATA){
      this.formSubmissionData = (this.taskSubmission && (this.taskSubmission.FORM_DATA === '' || this.taskSubmission.FORM_DATA === undefined) ? '' : JSON.parse(this.taskSubmission.FORM_DATA));
    }
    if(this.taskSubmission && this.taskSubmission.SERVER_TIMESTAMP && this.taskSubmission.LAST_UPDATED_AT){
    this.lastSubmissionTimestamp = (this.taskSubmission && (this.taskSubmission.SERVER_TIMESTAMP && this.taskSubmission.SERVER_TIMESTAMP !== null) ? this.taskSubmission.SERVER_TIMESTAMP : this.taskSubmission.LAST_UPDATED_AT);
    }
    // Get form resources depending on different platforms
    // FOR BROWSER
    if (!this.isHybridNative) {
      this.masterData = this.shareData.getMasterData();
    }
    this.formFileCompArray = [];

    // if (this.form && this.form.NESTED_FORMS && this.form.NESTED_FORMS != "") {
    //   let nestedFormObj = [];
    //   let nestedFormArr = JSON.parse(this.form.NESTED_FORMS);
    //   var element;
    //   for (element of nestedFormArr) {
    //     let str = element.selectedNestedFormId + element.selectedNestedFormVersion;
    //     let queryToFetchForm = `SELECT TEMPLATE FROM ${this.constants.FORM_TABLE} WHERE (FORM_ID || VERSION) IN ('${str}')`;

    //     let fetchForm: DbResult = await this.unviredSDK.dbExecuteStatement(queryToFetchForm);
    //     if (fetchForm.type === ResultType.success) {
    //       let formDB: FORM_HEADER[] = fetchForm.data;
    //       let formHeader: FORM_HEADER = formDB[0];
    //       if(formHeader){
    //         let formDesign = this.utilityFunction.decodeUnicode(formHeader.TEMPLATE);
    //         nestedFormObj.push({ selectedNestedFormId: element.selectedNestedFormId, selNestedFormVersion: element.selectedNestedFormVersion, formDesign: formDesign })
    //       }
    //     }
    //   }
    //   await this.getNestedFormsDetails(formIOComponent, nestedFormObj);
    //   var obj;
    //   for (obj of nestedFormObj) {
    //     await this.getMasterdDataresource(obj.formDesign, 'dataSrc', 'masterdata');
    //   }
    // }

    // Check file component in form components
    // JSON.stringify(formIOComponent, (_, nestedValue) => {
    //   if (nestedValue && nestedValue['type'] === 'file') {
    //     this.formFileCompArray.push(nestedValue.key);
    //     this.formFileCompObj.push(nestedValue)
    //   }
    //   return nestedValue;
    // });

    // this.fileUploadFlag = (this.formFileCompArray.length > 0) ? true : false;
    // if (this.form && this.form.RESOURCES && this.form.RESOURCES != "") {
    //   try {
    //     let FORM_IO_ENTITY = await this.getFormioMasterDataObjects(formIOComponent, 'dataSrc', 'json');
    //     if (FORM_IO_ENTITY != undefined && FORM_IO_ENTITY != '' && Object.keys(FORM_IO_ENTITY).length === 0) {
    //       this.formData = FORM_IO_ENTITY;
    //     } else {
    //       this.formData = formIOComponent;
    //     }
    //   } catch (error) {
    //     this.unviredSDK.logError('FormRendererPage', 'getObjects()', 'ERROR: ' + error);
    //     if (this.loader.isLoading) {
    //       this.unviredSDK.logInfo('FormRendererPage', 'ionViewDidEnter()', 'Hiding Busy indicator...')
    //       await this.loader.dismissBusyIndicator();
    //     }
    //   }
    // }

    // inject masterdata
    // this.unviredSDK.logInfo('FormRendererPage', 'prepareFormResourcesAndSetFormData()', 'calling getMasterdDataresource()');
    // await this.getMasterdDataresource(formIOComponent, 'dataSrc', 'masterdata');
    this.formData = formIOComponent;
    this.shareData.setFormIOComponent(this.taskSubmission, this.formData);
  }

  async prepareFormUploadsAndSubmissionData() {
    // TODO: Update the Flag submission data.
    // Replace the paths here.
    // Checking form submission data for files.
    if (this.fileUploadFlag) {
      let dbResponseForTaskSubmissionAttachment = {} as DbResult;
      dbResponseForTaskSubmissionAttachment = await this.unviredSDK.dbSelect(this.constants.TASK_SUBMISSION_ATTACHMENT_TABLE, `TAG1 = '${this.task.TASK_ID}' AND TAG2 = '${this.form.FORM_ID}' AND (TAG5 = 'A' OR TAG5 = '' OR TAG5 IS NULL)`);
      await this.showResponseWithToaster(dbResponseForTaskSubmissionAttachment, this.loader.isLoading);
      if (dbResponseForTaskSubmissionAttachment.type === ResultType.success && dbResponseForTaskSubmissionAttachment.data.length > 0) {
        this.attachmentDbResult = dbResponseForTaskSubmissionAttachment.data;
        for (var x1 = 0; x1 < this.attachmentDbResult.length; x1++) {
          let fileobj = { 'name': '', 'originalName': '', 'size': 0, 'storage': 'base64', 'type': '', 'url': '', 'key': '', 'uid': '' };
          fileobj.name = this.taskSubmission.SUBMISSION_ID + '~' + this.attachmentDbResult[x1].UID;
          let atchmtFileName = this.attachmentDbResult[x1].FILE_NAME;
          if (atchmtFileName.indexOf(this.attachmentDbResult[x1].UID) !== -1) {
            let subString = this.attachmentDbResult[x1].UID + '_';
            fileobj.originalName = atchmtFileName.replace(subString, '');
          } else {
            fileobj.originalName = this.attachmentDbResult[x1].FILE_NAME;
          }
          let sizeKey = this.attachmentDbResult[x1].TAG4.split('~');
          fileobj.size = Number(sizeKey[0]);
          fileobj.key = sizeKey[1];
          fileobj.type = this.attachmentDbResult[x1].MIME_TYPE;
          fileobj.uid = this.attachmentDbResult[x1].UID;
          for (let i = 0; i < this.formFileCompObj.length; i++) {
            if (this.formFileCompObj[i].type == sizeKey[1]) {
              fileobj.storage = this.formFileCompObj[i].storage;
            }
          }

          if (this.devicePlatform === 'browser') {
            fileobj.url = this.attachmentDbResult[x1].DATA;
          } else {
            let localFilePath: string = '';
            switch (true) {
              case (this.platform.is('android')):
                let dbFilePath = this.attachmentDbResult[x1].LOCAL_PATH;
                try {
                  let result = await this.unviredSDK.getAttachmentFolderPath();
                  let path = 'file://' + result.data + '/';
                  let res = await this.file.resolveLocalFilesystemUrl(path + this.attachmentDbResult[x1].FILE_NAME)
                  localFilePath = 'file://' + result.data + '/';

                } catch (error) {
                  localFilePath = 'file://' + dbFilePath.substring(0, dbFilePath.lastIndexOf('/') + 1);
                  this.unviredSDK.logError('FormRendererPage', 'getAttachmentFolderPath()', 'ERROR: ' + error);
                }
                break;
              case (this.platform.is('ios')):
                try {
                  let result = await this.unviredSDK.getAttachmentFolderPath();
                  localFilePath = 'file://' + result.data + '/';
                } catch (error) {
                  this.unviredSDK.logError('FormRendererPage', 'getAttachmentFolderPath()', 'ERROR: ' + error);
                }
                break;
              case (this.devicePlatform == 'windows'):
                localFilePath = 'ms-appdata:///local/Attachments/';
                break;
            }
            try {
              let base64Version: string = '';
              switch (true) {
                case (this.platform.is('android')):
                case (this.platform.is('ios')):
                  base64Version = await this.file.readAsDataURL(localFilePath, this.attachmentDbResult[x1].FILE_NAME);
                  fileobj.url = base64Version;
                  break;
                case (this.devicePlatform == 'windows'):
                  try {
                    let res = await this.file.resolveLocalFilesystemUrl(localFilePath + this.attachmentDbResult[x1].FILE_NAME)
                    base64Version = await this.file.readAsDataURL(localFilePath, `${this.attachmentDbResult[x1].FILE_NAME}`);
                    fileobj.url = base64Version;

                  } catch (error) {
                    base64Version = await this.file.readAsDataURL(localFilePath, `${this.attachmentDbResult[x1].UID}_${this.attachmentDbResult[x1].FILE_NAME}`);
                    fileobj.url = base64Version;
                  }
                  break;
              }
            } catch (error) {
              this.unviredSDK.logError('FormRendererPage', 'readAsDataURL()', 'ERROR: ' + error);
            }
          }
          this.fileArray.push(fileobj);
          this.fileAttachmentMap[fileobj.uid] = fileobj;
        }

        // Replace Form Submission Data
        var formSubmissionDataString = JSON.stringify(this.formSubmissionData)
        for (var attachmentId in this.fileAttachmentMap) {
          if (this.fileAttachmentMap.hasOwnProperty(attachmentId)) {
            formSubmissionDataString = formSubmissionDataString.replace("\"" + attachmentId + "\"", JSON.stringify(this.fileAttachmentMap[attachmentId]))
          }
        }
        this.formSubmissionData = JSON.parse(formSubmissionDataString)
        this.subData = this.formSubmissionData;
      } else {
        this.subData = this.formSubmissionData;
      }
    } else {
      this.subData = this.formSubmissionData;
    }
    this.unviredSDK.logDebug('formrenderpage', 'prepareFormUploadsAndSubmissionData()', 'submission data  = ' + JSON.stringify(this.formSubmissionData))
  }

  async getNestedFormsDetails(obj: any, nestedFormObj) {
    var objects = [];
    for (var i in obj) {
      if (typeof obj[i] == 'object') {
        objects = objects.concat(this.getNestedFormsDetails(obj[i], nestedFormObj));
      } else if (i == 'nestedFormComponenttype' && obj['nestedFormComponenttype'] == 'smartNestedForm') {
        let selNestedFormVersion = obj.selNestedFormVersion;
        let selectedNestedFormId = obj.selectedNestedFormId;

        if (selNestedFormVersion != undefined && selNestedFormVersion != "" && selectedNestedFormId != undefined && selectedNestedFormId != "") {
          let fDesign = nestedFormObj.filter((item: any) => {
            if (item.selectedNestedFormId == selectedNestedFormId && item.selNestedFormVersion == selNestedFormVersion) {
              return item.formDesign
            }
          });
          if(fDesign.length > 0){
            let formDesign = fDesign[0].formDesign;
            obj.formDesign = JSON.parse(formDesign);
            JSON.stringify(obj.formDesign, (_, nestedValue) => {
              if (nestedValue && nestedValue['type'] === 'file') {
                this.formFileCompArray.push(nestedValue.key);
              }
              return nestedValue;
            });
          }
          this.fileUploadFlag = (this.formFileCompArray.length > 0) ? true : false;
        }
      }
    }
    return true;
  }
  async getMasterdDataresource(obj: any, key: any, val: any) {
    var objects = [];

    for (var i in obj) {
      if (typeof obj[i] == 'object') {
        objects = objects.concat(await this.getMasterdDataresource(obj[i], key, val));
      } else if (i == key && obj[key] == val) {
        var val1 = obj['valueProperty'];
        var ress;
        if (obj.data && typeof obj.data == 'object' && obj.data != undefined) {
          if (obj.data['masterdata']) {
            ress = obj.data['masterdata'];
          }
        }
        var obj1 = [];

        if (ress != undefined && ress != "") {
          let masterdataResourcequery = `SELECT * FROM ${this.constants.MASTER_DATA_TABLE} WHERE RESOURCE_NAME = '${ress}'`;

          let masterdataResults: DbResult = await this.unviredSDK.dbExecuteStatement(masterdataResourcequery);

          if (masterdataResults.type == 0 && masterdataResults.data) {
            if (masterdataResults.data.length > 0) {
              for (let i = 0; i < masterdataResults.data.length; i++) {
                if (masterdataResults.data[i].DATA) {
                  obj1.push(JSON.parse(masterdataResults.data[i].DATA));
                }
              }
            } else {
            }
          }
        } else {
          this.unviredSDK.logInfo('FormRendererPage', 'getMasterdDataresource()', 'selected masterdata resource is empty');
          obj1 = [];
        }
        var tmplt = obj['template'];
        if (tmplt != "") {
          var str1 = tmplt.substring(tmplt.lastIndexOf('.') + 1, tmplt.length);
          var str2 = str1.substring(0, str1.indexOf('}'), str1.length);
          var str3 = "<span>{{" + "item." + str2.trim() + "}}</span>";
          obj['template'] = str3;
        }
        var val = obj['valueProperty'];
        var valProp = '';
        if (val != "") {
          valProp = val.substring(val.lastIndexOf('.') + 1, val.length);
          obj['valueProperty'] = valProp;
        }
        str2 = str2.trim();
        obj1 = obj1.sort(function (a, b) { return (a[str2] > b[str2]) ? 1 : ((a[str2] < b[str2]) ? -1 : 0); });
        obj.masterdata = obj1;
      }
    }
  }
  prepareTabulatorData() {
    try {
      if ((($('.table').find("th").length > 0)) || ($(".list-group-header").length > 0)) {
        this.alignTableContents();
        $('head').append('<link rel="stylesheet" href="assets/css/responsive-grid.min.css" type="text/css" />');
      }
    } catch (err) {
      console.log("err " + err)
    }
  }

  // async setFormInitializedData() {
  //   // Get form data when form initialized
  //   try {
  //     let initialFormData = await returnTempData();
  //     console.log("initialFormData = " + JSON.stringify(initialFormData))
  //     if(initialFormData){
  //       this.formInitializedWith = JSON.stringify(initialFormData.tempData.data);
  //     }
  //   } catch (error) {
  //     this.unviredSDK.logError('FormRendererPage', 'setFormInitializedData()', 'ERROR: ' + error);
  //   }
  // }

  async updateDB(submission: any, pMode: string) {
    let dbResponseForTaskSubmission = {} as DbResult;
    try {
      // this.updatedTaskSubmission = this.taskSubmission;
      // if (this.task.TASK_TYPE === this.constants.TASK_TYPE.APPROVAL && this.isRequestApproval) {
      //   this.updatedTaskSubmission.USER_STATUS = this.constants.TASK_USER_STATUS.IN_REQUEST;
      // } else if (this.task.TASK_TYPE === this.constants.TASK_TYPE.APPROVAL && this.isSubmitFormViaActionButton) {
      //   this.updatedTaskSubmission.USER_STATUS = this.constants.TASK_USER_STATUS.COMPLETED;
      // } else {
      //   this.updatedTaskSubmission.USER_STATUS = ((this.completeFlag && this.markComplete) || this.isPrimaryUserCompleted) ? this.constants.TASK_USER_STATUS.COMPLETED : this.taskUser.STATUS;
      // }

      // this.shareData.setTaskSubmissionData(this.taskSubmission);
      if (this.fileUploadFlag) {
        if (this.formFileCompArray.length > 0) {
          this.formFileCompArray.forEach(element => {
            var that = this;
            JSON.stringify(submission.data, (_, nestedValue) => {
              if (nestedValue && nestedValue[element]) {
                if (nestedValue[element].length > 0) {
                  nestedValue[element].forEach(item => {
                    if (item === Object(item)) {
                      item['key'] = element;
                      if (!item['uid']) {
                        item['uid'] = that.utilityFunction.generateUUID();
                      }
                      that.uploadedFiles.push(item);
                      for (let index = 0; index < nestedValue[element].length; index++) {
                        const elementinNestedValue = nestedValue[element][index];
                        if (elementinNestedValue.name == item['name']) {
                          nestedValue[element][index] = item['uid'];
                        }
                      }
                    }
                  });
                }
              }
              return nestedValue;
            });
          });
          // this.updatedTaskSubmission.FORM_DATA = JSON.stringify(submission.data);
          this.permitFormSubmissionData.DATA = JSON.stringify(submission.data);
          this.lastFormData = submission.data;
        }
      } else {
        // this.updatedTaskSubmission.FORM_DATA = JSON.stringify(submission.data);
        this.permitFormSubmissionData.DATA = JSON.stringify(submission.data);
      }
      try {

        // Update task_submission properties
        this.permitFormSubmissionData.PERMIT_NO = this.permitHeaderData.PERMIT_NO;
        this.permitFormSubmissionData.FORM_NAME = this.form.FORM_NAME;
        this.permitFormSubmissionData.FORM_TITLE = this.form.FORM_TITLE;
        this.permitFormSubmissionData.FORM_VERSION = this.form.FORM_VERSION;
        this.permitFormSubmissionData.FORM_ID = this.form.FORM_ID;
        this.permitFormSubmissionData.FORM_GUID = this.unviredSDK.guid().replace(/-/g, '');
        this.permitFormSubmissionData.P_MODE = pMode;
        this.permitFormSubmissionData.OBJECT_STATUS = this.constants.OBJECT_STATUS.MODIFY;
        this.permitFormSubmissionData.LID = this.permitFormSubmissionData.LID ? this.permitFormSubmissionData.LID : this.utilityFunction.generateUUID();
        // this.permitFormSubmissionData.OBJECT_STATUS = this.constants.OBJECT_STATUS.ADD;
        this.permitFormSubmissionData.FID = this.permitHeaderData.LID ? this.permitHeaderData.LID : "";
        if(this.markComplete){
        this.permitFormSubmissionData.PARTIAL_FLAG = AppConstants.NO;
        this.permitFormSubmissionData.COMPLETED = AppConstants.YES;
        }else{
          this.permitFormSubmissionData.PARTIAL_FLAG = AppConstants.YES;
        this.permitFormSubmissionData.COMPLETED = AppConstants.NO;
        }
        this.permitFormSubmissionData.CHANGED_ON = (new Date()).getTime();
        let userContext: any = localStorage.getItem('userContext');
        let user = '';
        if (userContext) {
          userContext = JSON.parse(userContext);
        }

        if (userContext?.USER_CONTEXT_HEADER) {
          user =
            userContext?.USER_CONTEXT_HEADER?.FIRST_NAME +
            ' ' +
            userContext?.USER_CONTEXT_HEADER?.LAST_NAME;
        }
        this.permitFormSubmissionData.CHANGED_BY = user;

        // Update task_submission database with its updated value
        // dbResponseForTaskSubmission = await this.unviredSDK.dbUpdate(this.constants.PERMIT_FORM, this.permitFormSubmissionData, `PERMIT_NO = '${this.permitFormSubmissionData.PERMIT_NO}' AND FORM_ID = '${this.permitFormSubmissionData.FORM_ID}'`);
        // let attachmentquery = `SELECT * FROM ${this.constants.TASK_SUBMISSION_ATTACHMENT_TABLE} WHERE FID = '${this.updatedTaskSubmission.LID}'`;
        // let fetchAttchments: DbResult = await this.unviredSDK.dbExecuteStatement(attachmentquery);

        // await this.showResponseWithToaster(dbResponseForTaskSubmission, this.loader.isLoading);
        // if (this.fileUploadFlag) {
        //   let that = this;
        //   if (this.uploadedFiles && this.uploadedFiles.length > 0) {
        //     if (that.attachmentDbResult && that.attachmentDbResult.length > 0) {
        //       that.attachmentDbResult.forEach((ff: any) => {
        //         var countt = 0;
        //         for (var tt = 0; tt < that.uploadedFiles.length; tt++) {
        //           if (ff.TAG3 == that.uploadedFiles[tt].name) {
        //             countt++;
        //           }
        //         }
        //         if (countt == 0) {
        //           that.deletedArray.push(ff);
        //         }
        //       });

        //       that.uploadedFiles.forEach((files) => {
        //         var count = 0;
        //         for (var ss = 0; ss < that.attachmentDbResult.length; ss++) {
        //           if (files.name == that.attachmentDbResult[ss].TAG3) {
        //             count++;
        //           }
        //         }
        //         if (count == 0) {
        //           that.addedArray.push(files);
        //         }
        //       });
        //     } else {
        //       // for (var s2 = 0; s2 < this.uploadedFiles.length; s2++) {
        //       //   await this.createAttachmentItem(this.uploadedFiles[s2].originalName, this.uploadedFiles[s2].url, this.uploadedFiles[s2].type, this.task.TASK_ID, this.form.FORM_ID, this.uploadedFiles[s2].name, this.uploadedFiles[s2].size, "A", this.uploadedFiles[s2].uid, this.uploadedFiles[s2].key);
        //       // }
        //     }
        //   } else {
        //     let dbResponse = await this.unviredSDK.dbSelect(this.constants.TASK_SUBMISSION_ATTACHMENT_TABLE, `TAG1 = '${this.task.TASK_ID}' AND TAG2 = '${this.form.FORM_ID}' AND (TAG5 = 'A' OR TAG5 = '' OR TAG5 IS NULL)`);
        //     await this.showResponseWithToaster(dbResponse, this.loader.isLoading);

        //     if (dbResponse.type === ResultType.success && dbResponse.data.length > 0) {
        //       let attachmentsDbResult = dbResponse.data;
        //       for (let att = 0; att < attachmentsDbResult.length; att++) {
        //         await this.createAttachmentItem(attachmentsDbResult[att].originalName, attachmentsDbResult[att].DATA, attachmentsDbResult[att].MIME_TYPE, this.task.TASK_ID, this.form.FORM_ID, attachmentsDbResult[att].TAG3, attachmentsDbResult[att].TAG4, "D", attachmentsDbResult[att].UID, '');
        //       }
        //     }
        //   }
        // }
      } catch (error) {
        this.unviredSDK.logError('FormRendererPage', 'updateDB()', 'Caught Error: ' + JSON.stringify(error))
        if (this.loader.isLoading) {
          this.unviredSDK.logInfo('FormRendererPage', 'updateDB()', 'Hiding Busy indicator...')
          await this.loader.dismissBusyIndicator();
        }
        this.toastr.error('', this.translate.instant('Error updating form!'), { timeOut: 1500 });
        await this.loader.showAlert('Error', this.translate.instant('Record not inserted!'));
      }
    } catch (error:any) {
      this.unviredSDK.logError('FormRendererPage', 'updateDB()', 'Caught Error: ' + JSON.stringify(error))
      if (this.loader.isLoading) {
        this.unviredSDK.logInfo('FormRendererPage', 'updateDB()', 'Hiding Busy indicator...')
        await this.loader.dismissBusyIndicator();
      }
      await this.loader.showAlert('Error', error);
    }
  }

  async draftForm(mode: any) {
    try {
      if (!this.loader.isLoading) {
        await this.loader.showBusyIndicator(this.translate.instant('Saving Form...'), 'crescent');
      }
      let val = await returnTempData();
      if (mode === 'yes') {
        let tempData = val.tempData ? val.tempData : val.submission;
        await this.updateDataAndSendToServer(tempData, this.constants.TASK_MODE.P_MODE_A);
      } else {
        if (val.tempData !== '') {
          this.updateDataOnDraftForm(val.tempData);
        } else {
          let clientCompField = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "client-complete-check-field" }) : [];
          let completeFld = (clientCompField.length > 0 && val.submission && val.submission.data !== "" && clientCompField[0].value !== "") ? val.submission.data[clientCompField[0].value] : true;
          if (!completeFld) {
            this.updateDataOnDraftForm(val.submission);
          }
        }
      }
    } catch (error) {
      if (this.loader.isLoading) {
        this.unviredSDK.logInfo('FormRendererPage', 'draftForm()', 'Hiding Busy indicator...')
        await this.loader.dismissBusyIndicator();
      }
      this.unviredSDK.logError('FormRendererPage', 'draftForm()', 'ERROR: ' + error);
    }
  }

  async updateDataOnDraftForm(data: any) {
    if (this.devicePlatform === 'browser') {
      await this.updateDataAndSendToServer(data, this.constants.TASK_MODE.P_MODE_A);
    } else {
      await this.updateDB(data, this.constants.TASK_MODE.P_MODE_A);
      // saving attachments when added multiple files.
      // if (this.addedArray && this.addedArray.length > 0) {
      //   for (var s6 = 0; s6 < this.addedArray.length; s6++) {
      //     await this.createAttachmentItem(this.addedArray[s6].originalName, this.addedArray[s6].url, this.addedArray[s6].type, this.task.TASK_ID, this.form.FORM_ID, this.addedArray[s6].name, this.addedArray[s6].size, "A", this.addedArray[s6].uid, this.addedArray[s6].key);
      //   }
      // }
      await this.unviredSDK.unlockDataSender();
      this.fetchData.checkUMPCallDone.next(true);
      this.sendAnalystDataToServer();
      this.router.navigate(['/permits']);
      if (this.loader.isLoading) {
        this.unviredSDK.logInfo('FormRendererPage', 'draftForm()', 'Hiding Busy indicator...')
        await this.loader.dismissBusyIndicator();
      }
    }
  }

  async saveForm() {
    localStorage.setItem('renderBackFormDataChanged', 'true');
    if (!this.loader.isLoading) {
      await this.loader.showBusyIndicator(this.markComplete ? this.translate.instant('Completing Form...') : this.translate.instant('Saving Form...'), 'crescent');
      // await this.loader.showBusyIndicator(this.completeFlag ? this.translate.instant('Completing Form...') : this.translate.instant('Saving Form...'), 'crescent');
    }
    // Check the task is completed
    // if (this.task.TASK_STATUS !== this.constants.TASK_STATUS.COMPLETED) {
      // Check the user has marked this task as completed
      // if (this.taskUser.STATUS !== this.constants.TASK_USER_STATUS.COMPLETED) {
        // Update, save and send the data to server also close the screen
        if (this.formData.display === 'wizard') {
          // FORM DATA TYPE WIZARD
          try {
            let val = await wizardSubmit();
            if (val.submission !== '') {
              let clientCompField = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "client-complete-check-field" }) : [];
              let completeFld: boolean = true;
              if (clientCompField.length > 0 && val.submission && val.submission.data && clientCompField[0].value !== "") {
                completeFld = val.submission.data[clientCompField[0].value]
              }
              if (!completeFld && this.markComplete) {
                await this.confirmationAlert();
                // this.confirmationAlert(this.translate.instant('Form cannot be completed until all required fields are entered. Save instead?'));
              } else {
                let arr = [];
                let keys = Object.keys(val.submission.data)
                Object.values(val.submission.data).forEach((ele, i) => {
                  if (ele === "") {
                    arr.push(keys[i]);
                  }
                });
                this.additionalDataEve = JSON.stringify(arr);

                let promptOnFormComplete: boolean = false;
                let promptOnFormCompleteData = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "prompt-on-form-complete" }) : [];
                if (promptOnFormCompleteData && promptOnFormCompleteData.length > 0 && promptOnFormCompleteData[0]) {
                  promptOnFormComplete = promptOnFormCompleteData[0].value
                  if (promptOnFormComplete && this.markComplete) {
                    let msgFormComplete = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "message-form-complete" }) : [];
                    if (msgFormComplete && msgFormComplete.length > 0 && msgFormComplete[0]) {
                      let res = await this.confirmationAlertForComplete(msgFormComplete[0].value);
                      console.log("res" + res)

                      if (res) {
                        if (!this.loader.isLoading) {
                          await this.loader.showBusyIndicator(this.translate.instant('Completing Form...'), 'crescent');
                        }
                        await this.updateDataAndSendToServer(val.submission, this.constants.TASK_MODE.P_MODE_M);
                      }
                    }
                  } else {
                    if (!this.markComplete) {
                      await this.confirmationAlert();
                    } else {
                      await this.updateDataAndSendToServer(val.submission, this.constants.TASK_MODE.P_MODE_M);
                    }
                  }
                }
              }
            }
          } catch (err:any) {
            if (this.loader.isLoading) {
              this.unviredSDK.logInfo('FormRendererPage', 'saveForm()', 'Hiding Busy indicator...')
              await this.loader.dismissBusyIndicator();
            }
            if (err.error !== '') {
              if (err.error === 'validation error') {
                if (this.completeFlag) {
                  // if (this.taskUser.USER_ID === this.task.PRIMARY_USER) {
                  // this.primaryUserValidationAlert(this.translate.instant('Please fill all mandatory fields to mark complete!'))
                  // this.confirmationAlert(this.translate.instant('Please fill all mandatory fields to mark complete!'))
                  // this.confirmationAlert(this.translate.instant('Form is partially filled. Still mark complete?'))
                  await this.confirmationAlert();
                  this.completeFlag = false;
                  // } else {
                  //   this.confirmationAlert(this.translate.instant('Form is not filled completely.  Proceed to mark complete?'));
                  // }
                } else {
                  await this.confirmationAlert();
                  // this.confirmationAlert(this.translate.instant('Form cannot be completed until all required fields are entered. Save instead?'));
                }
              } else {
                let msg1 = '';
                if (err.error.length > 0) {
                  for (var e11 = 0; e11 < err.error.length; e11++) {
                    msg1 = msg1 + "<br />" + err.error[e11].message;
                  }
                  msg1 = msg1.slice(6);
                  this.loader.showToast(msg1);
                } else {
                  this.loader.showToast(JSON.stringify(err.error));
                }
              }
            }
          }
        } else if (this.formData.display === 'form') {
          // FORM DATA TYPE IS FORM
          try {
            let val = await submit();
            if (val.submission !== '') {
              let clientCompField = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "client-complete-check-field" }) : [];
              let completeFld: boolean = true;
              if (clientCompField.length > 0 && val.submission && val.submission.data && clientCompField[0].value !== "") {
                completeFld = val.submission.data[clientCompField[0].value]
              }
              if (!completeFld && this.markComplete) {
                await this.confirmationAlert();
                // this.confirmationAlert(this.translate.instant('Form cannot be completed until all required fields are entered. Save instead?'));
              } else {
                let arr = [];
                let keys = Object.keys(val.submission.data)
                Object.values(val.submission.data).forEach((ele, i) => {
                  if (ele === "") {
                    arr.push(keys[i]);
                  }
                });
                this.additionalDataEve = JSON.stringify(arr)
                let promptOnFormComplete: boolean = false;
                let promptOnFormCompleteData = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "prompt-on-form-complete" }) : [];
                if (promptOnFormCompleteData && promptOnFormCompleteData.length > 0 && promptOnFormCompleteData[0]) {
                  promptOnFormComplete = promptOnFormCompleteData[0].value
                  if (promptOnFormComplete && this.markComplete) {
                    let msgFormComplete = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "message-form-complete" }) : [];
                    if (msgFormComplete && msgFormComplete.length > 0 && msgFormComplete[0]) {
                      let res = await this.confirmationAlertForComplete(msgFormComplete[0].value);
                      console.log("res" + res)
                      if (res) {
                        if (!this.loader.isLoading) {
                          await this.loader.showBusyIndicator(this.translate.instant('Completing Form...'), 'crescent');
                        }
                        await this.updateDataAndSendToServer(val.submission, this.constants.TASK_MODE.P_MODE_M);
                      }
                    }
                  } else {
                    if (!this.markComplete) {
                      await this.confirmationAlert();
                    } else {
                      console.log("updateDataAndSendToServer at line 1173")
                      await this.updateDataAndSendToServer(val.submission, this.constants.TASK_MODE.P_MODE_M);
                    }
                  }
                } else {
                  if (!this.markComplete) {
                    await this.confirmationAlert();
                  } else {
                    console.log("updateDataAndSendToServer at line 1181")
                    await this.updateDataAndSendToServer(val.submission, this.constants.TASK_MODE.P_MODE_M);
                  }
                }

                // await this.updateDataAndSendToServer(val.submission, this.constants.TASK_MODE.P_MODE_M);
              }
            }
          } catch (err:any) {
            // if (this.loader.isLoading) {
            //   this.unviredSDK.logInfo('FormRendererPage', 'saveForm()', 'Hiding Busy indicator...')
            //   console.log("dismissing loading controller line 632 " + new Date().getTime(), new Date().toLocaleString())
            // await this.loader.dismissBusyIndicator();
            // }
            if (err.error !== '') {
              if (err.error === 'validation error') {
                let components = this.formData.components;
                let isApprovalform = false;
                for (let i = 0; i < components.length; i++) {
                  if (components[i].type === "form" && components[i].tags && components[i].tags.length > 0) {
                    for (let j = 0; j < components[i].tags.length; j++) {
                      if (components[i].tags[j] === "Approval") {
                        isApprovalform = true;
                      }
                    }
                  }
                }
                if (this.completeFlag) {
                  // if (this.taskUser.USER_ID === this.task.PRIMARY_USER) {
                  //   this.primaryUserValidationAlert(this.translate.instant('Please fill all mandatory fields to mark complete!'))
                    // this.confirmationAlert(this.translate.instant('Please fill all mandatory fields to mark complete!'))
                    this.completeFlag = false;
                  // } else {
                    await this.confirmationAlert();
                    // this.confirmationAlert(this.translate.instant('Form is not filled completely. Proceed to mark complete?'));
                  // }
                } else if (isApprovalform) {
                  // this.primaryUserValidationAlert(this.translate.instant('Please fill all mandatory fields to Proceed!'))
                  // this.confirmationAlert(this.translate.instant('Please fill all mandatory fields to Proceed!'))
                  isApprovalform = false;
                  await this.confirmationAlert();

                } else {
                  await this.confirmationAlert();
                  // this.confirmationAlert(this.translate.instant('Form cannot be completed until all required fields are entered. Save instead?'));
                }
              } else {
                let msg = '';
                if (err.error.length > 0) {
                  for (var e1 = 0; e1 < err.error.length; e1++) {
                    msg = msg + "<br />" + err.error[e1].message;
                  }
                  msg = msg.slice(6);
                  this.loader.showToast(msg);
                } else {
                  this.loader.showToast(JSON.stringify(err.error));
                }
                if (this.loader.isLoading) {
                  this.unviredSDK.logInfo('FormRendererPage', 'saveForm()', 'Hiding Busy indicator...')
                  await this.loader.dismissBusyIndicator();
                }
              }
            }
            // if (this.loader.isLoading) {
            //   this.unviredSDK.logInfo('FormRendererPage', 'saveForm()', 'Hiding Busy indicator...')
            //   console.log("dismissing loading controller line 632 " + new Date().getTime(), new Date().toLocaleString())
            // await this.loader.dismissBusyIndicator();
            // }
          }
        } else {
          // other types of forms
          try {
            let val = await submit();
            if (val.submission !== '') {
              let clientCompField = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "client-complete-check-field" }) : [];
              let completeFld: boolean = true;
              if (clientCompField.length > 0 && val.submission && val.submission.data && clientCompField[0].value !== "") {
                completeFld = val.submission.data[clientCompField[0].value]
              }
              if (!completeFld) {
                await this.confirmationAlert();
                // this.confirmationAlert(this.translate.instant('Form cannot be completed until all required fields are entered. Save instead?'));
              } else {
                let arr = [];
                let keys = Object.keys(val.submission.data)
                Object.values(val.submission.data).forEach((ele, i) => {
                  if (ele === "") {
                    arr.push(keys[i]);
                  }
                });
                this.additionalDataEve = JSON.stringify(arr);
                await this.updateDataAndSendToServer(val.submission, this.constants.TASK_MODE.P_MODE_M);
              }
            }
          } catch (err:any) {
            if (this.loader.isLoading) {
              this.unviredSDK.logInfo('FormRendererPage', 'saveForm()', 'Hiding Busy indicator...')
              await this.loader.dismissBusyIndicator();
            }
            if (err.error !== '') {
              if (err.error === 'validation error') {
                if (this.completeFlag) {
                  // if (this.taskUser.USER_ID === this.task.PRIMARY_USER) {
                  // this.primaryUserValidationAlert(this.translate.instant('Please fill all mandatory fields to mark complete!'))
                  // this.confirmationAlert(this.translate.instant('Please fill all mandatory fields to mark complete!'))
                  await this.confirmationAlert();
                  this.completeFlag = false;
                  // } else {
                  //   this.confirmationAlert(this.translate.instant('Form is not filled completely. Proceed to mark complete?'));
                  // }
                } else {
                  await this.confirmationAlert();
                  // this.confirmationAlert(this.translate.instant('Form cannot be completed until all required fields are entered. Save instead?'));
                }
              } else {
                this.loader.showToast(err.error);
              }
            }
          }
        }
      // } else {
      //   if (this.loader.isLoading) {
      //     this.unviredSDK.logInfo('FormRendererPage', 'saveForm()', 'Hiding Busy indicator...')
      //     await this.loader.dismissBusyIndicator();
      //   }
      //   // Task user has completed the task so discard all changes and close the screen
      //   this.completionAlert(this.translate.instant("Success"), this.translate.instant("Form completed. No further action required."));
      // }
    // } else {
    //   if (this.loader.isLoading) {
    //     this.unviredSDK.logInfo('FormRendererPage', 'saveForm()', 'Hiding Busy indicator...')
    //     await this.loader.dismissBusyIndicator();
    //   }
    //   // Task is completed so discard all changes and close the screen
    //   this.completionAlert(this.translate.instant("Success"), this.translate.instant("Form completed. No further action required."));
    // }
    // await this.loadFormAnalystData({ "event": this.completeFlag ? "COMPLETE" : "SAVE", "fieldName": "", "timestamp": new Date().getTime(), "sequenceNumber": this.seqEve, "additionalData": this.completeFlag ? this.additionalDataEve : "" });
    // if (this.loader.isLoading) {
    //   console.log("dismissing loading controller line 632 " + new Date().getTime(), new Date().toLocaleString())
    // await this.loader.dismissBusyIndicator();
    // }
  }

  alignTableContents() {
    let activeMode = false;
    if (this.platform.is('mobile') && ($(window).width() < 760)) {
      activeMode = true;
    }
    insertContents();
    flexTable();

    window.onresize = () => {
      if (($('.table').find("th").length > 0) || ($(".list-group-header").length > 0)) {
        flexTable();
      }
    };

    function flexTable() {
      if (($(window).width() < 760) || activeMode) { // window is less than 768px
        showTableContents();
      } else {
        $(".list-group-header").show();
        $(".datagrid-table").each(function (tabBorderedVal) {
          $(".table").each(function (tabBorderedVal) {
            if ($(this).find('table').length == 0) {
              $(this).find(".table-bordered-thead").show();
              $(this).find('thead').show();
              $(this).find('tr').removeClass('datagrid-table-row')
              $(this).find('tr').find('td').removeClass('datagrid-table-data');
            }
          })
        });
      }
    }

    function showTableContents() {
      $(".list-group-header").hide();
      $(".datagrid-table").each(function (tabBorderedVal) {
        $(".table").each(function (val) {
          if ($(this).find('table').length == 0) {
            $(this).find(".table-bordered-thead").show();
            $(this).find('thead').hide();
            if ($(window).width() < 760) {
              $(this).find('tr').addClass('datagrid-table-row')
              $(this).find('tr').find('td').addClass('datagrid-table-data');
              $(".table").css("table-layout", "fixed");
            } else {
              $(".table").each(function (tabBorderedVal) {
                if ($(this).find('table').length == 0) {
                  $(this).find('tr').removeClass('datagrid-table-row')
                  $(this).find('tr').find('td').removeClass('datagrid-table-data');
                }
              })

              $(".datagridcard").each(function (datagridcardVal) {
                $(".datagrid-table").each(function (tabBorderedVal) {
                  if ($(this).find('table').length == 0) {
                    $(this).find('tr').addClass('datagrid-table-row')
                    $(this).find('tr').addClass('col-md-4')
                    $(this).find('tr').addClass('col-lg-3')

                    $(this).find('tr').find('td').addClass('datagrid-table-data');
                    $(".datagrid-table-row").css("border", "none");
                    $(".datagrid-table-row").css("margin-top", "10px");
                  }
                })
              });
            }
          }
        });
      })

      $(".table").each(function (value) {
        if ($(this).find('table').length == 0) {
          var tabledata = $(this).find('td');
          $(tabledata).each(function (val) {
            if ($(this)[0].children && $(this)[0].children[1] && $(this)[0].children[1].nodeName != undefined && $(this)[0].children[1].nodeName == "BUTTON") {
              if ($(this)[0].innerText.includes("Add")) {
                $(this).find(".table-bordered-thead").hide();
              }
            }
          })
        }
        $(this).find('tfoot').each(function (val) {
          $(this).find('.table-bordered-thead').hide();
        });
      })
    }

    $(document).off().on('click', '.btn', (e) => {
      if ($(window).width() < 760) {
        if (e && e.currentTarget) {
          var val = e.currentTarget.getAttribute("ref");
          var name = e.currentTarget.getAttribute("name");

          if (val != null) {
            if (val.includes("editgrid")) {
              $(".list-group-header").hide();
            } else if ((val.includes("addRow")) || (val.includes("removeRow")) || (name && name.includes("updateTask")) || (name && name.includes("resetTask"))) {
              $(".list-group-header").hide();
              $("table").find('thead').hide();
              insertContents();
              flexTable();
            }
          } else if (e.currentTarget.nodeName === 'BUTTON' && ((e.currentTarget.outerHTML.includes('fa-edit') || (e.currentTarget.outerHTML.includes('fa-trash'))))) {
            if ($(".list-group-header").length > 0) {
              $(".list-group-header").hide();
            }
          }
        }
      }
    });


    function insertContents(this: any) {
      if ($(window).width() < 760) {
        $(".datagrid-table").each(function (tabBorderedVal) {

          $('.table').each(function (obj) {
            if ($(this).find('table').length == 0) {
              let $pathis = $(this);
              // fetch the table heading and map with table row entry
              let tHeadings = $pathis.find("th");
              $pathis.find("tr").each(function (trObj) {
                $(this).addClass('table-row');
                let $thisObj = $(this);
                $thisObj.find("td").each(function (tdObj) {
                  let classList: any;
                  let $this = $(this);
                  if ($(this)[0] && $(this)[0].firstElementChild && $(this)[0].firstElementChild.classList) {
                    classList = $(this)[0].firstElementChild.classList;
                  }
                  if (classList && (classList.contains('formio-button-remove-row') || classList.contains('formio-button-add-row'))) {

                  } else {
                    $this.addClass('table-td');
                    if ($(this).find(".table-bordered-thead").length == 0 && tHeadings[tdObj] != undefined) {
                      let text = tHeadings[tdObj].innerText;
                      $this.append('<div class="table-bordered-thead">' + text + '</div> ');
                      $(".formio-component-label-hidden").css("order", "2");
                      $('.table-bordered-thead').hide();
                    } else if ($(this).find(".table-bordered-thead").length > 0 && tHeadings[tdObj] != undefined) {
                      $(".formio-component-label-hidden").css("order", "2");
                    }
                  }
                });
              });
            }
          })
        });

        $(".table").each(function (value) {
          if ($(this).find('table').length == 0) {
            var tabledata = $(this).find('td');
            $(tabledata).each(function (val) {
              if ($(this)[0].children && $(this)[0].children[1] && $(this)[0].children[1].nodeName != undefined && $(this)[0].children[1].nodeName == "BUTTON") {
                if ($(this)[0].innerText.includes("Add")) {
                  $(this).find(".table-bordered-thead").hide();
                }
              }
            })
          }
        })

        $(this).find('tfoot').each(function (val) {
          $(this).find('.table-bordered-thead').hide()
        })
      }
    }

  }

  async updateDataAndSendToServer(formSubmissionData: any, pMode: string) {
    try {
    let selectedPermit = localStorage.getItem('selectedPermit');

    let permit;
    if(selectedPermit){
      permit = JSON.parse(selectedPermit);
    }
    this.permitHeaderData = permit
      let fetchTaskSubmission: DbResult = await this.unviredSDK.dbExecuteStatement(`SELECT * FROM ${this.constants.PERMIT_FORM} WHERE PERMIT_NO = '${permit.PERMIT_NO}' AND FORM_ID = '${this.form.FORM_ID}'`);
      console.log("fetchTaskSubmission" + fetchTaskSubmission)
      if (fetchTaskSubmission.type === ResultType.success) {
        let latestSubmissionDB: PERMIT_FORM[] = fetchTaskSubmission.data;
        let latestSubmission: PERMIT_FORM = latestSubmissionDB[0];
        let latestSubmissionTimestamp: number;
        if (this.isHybridNative) {
          // if (latestSubmission.OBJECT_STATUS === this.constants.OBJECT_STATUS.ADD) {
            // For newly created task submissions, send P_MODE as A.
            await this.updateDB(formSubmissionData, this.constants.TASK_MODE.P_MODE_A);
            await this.sendTaskSubmissionToserver(this.permitFormSubmissionData);
            // await this.sendTaskUserToServer();
            await this.unviredSDK.unlockDataSender();
            this.fetchData.checkUMPCallDone.next(true);
            this.sendAnalystDataToServer();
            this.router.navigate(['/permits']);
            if (this.loader.isLoading) {
              this.unviredSDK.logInfo('FormRendererPage', 'updateDataAndSendToServer()', 'Hiding Busy indicator...')
              await this.loader.dismissBusyIndicator();
            }
          // } else {
          //   latestSubmissionTimestamp = (latestSubmission.SERVER_TIMESTAMP && latestSubmission.SERVER_TIMESTAMP !== null) ? latestSubmission.SERVER_TIMESTAMP : latestSubmission.LAST_UPDATED_AT;
          //   // Check server timestamp values to identify the submission is updated
          //   if (latestSubmissionTimestamp === this.lastSubmissionTimestamp) {
          //     // Both timesatmp value are same so submission is not updated, perform save and submit
          //     this.unviredSDK.logDebug('FormRendererPage', 'updateDataAndSendToServer()', 'Updating DB before network call...')
          //     await this.updateDB(formSubmissionData, pMode);
          //     this.unviredSDK.logDebug('FormRendererPage', 'updateDataAndSendToServer()', 'Updating DB Complete. Sending Task Submission....')
          //     await this.sendTaskSubmissionToserver(this.updatedTaskSubmission);
          //     // this.unviredSDK.logDebug('FormRendererPage', 'updateDataAndSendToServer()', 'Sending Task Submission Complete. Sending Task User....')
          //     // await this.sendTaskUserToServer();
          //     this.unviredSDK.logDebug('FormRendererPage', 'updateDataAndSendToServer()', 'Sending Task User Complete. Unlocking Data sender....')
          //     await this.unviredSDK.unlockDataSender();
          //     this.unviredSDK.logDebug('FormRendererPage', 'updateDataAndSendToServer()', 'Unlock Data Sender Complete. Checking whether all calls are complete in order to go back.')
          //     this.fetchData.checkUMPCallDone.next(true);
          //     this.sendAnalystDataToServer();
          //     this.router.navigate(['home']);
          //     if (this.loader.isLoading) {
          //       this.unviredSDK.logInfo('FormRendererPage', 'updateDataAndSendToServer()', 'Hiding Busy indicator...')
          //       await this.loader.dismissBusyIndicator();
          //     }
          //     this.unviredSDK.logDebug('FormRendererPage', 'updateDataAndSendToServer()', 'Everything is done. Hiding Busy Indicator and going back to previous page.')
          //   } else {
          //     let lastSubmissionUpdatedAt: string = this.utilityFunction.formatMessageByTimestamp(this.lastSubmissionTimestamp);
          //     let username: string = await this.appSpecificUtility.getUserName(latestSubmission.LAST_UPDATED_BY);
          //     const msg = this.translate.instant("CONFIRM_OVERWRITE", { SubmissionUpdatedAt: lastSubmissionUpdatedAt, userName: username });
          //     const alert = await this.alertController.create({
          //       header: this.translate.instant('Confirmation'),
          //       message: msg,
          //       animated: true,
          //       backdropDismiss: false,
          //       buttons: [
          //         {
          //           text: this.translate.instant('No'),
          //           handler: async () => {
          //             // Discard all changes
          //             await this.unviredSDK.unlockDataSender();
          //             this.fetchData.checkUMPCallDone.next(true);
          //             this.sendAnalystDataToServer();
          //             this.router.navigate(['home']);
          //             if (this.loader.isLoading) {
          //               this.unviredSDK.logInfo('FormRendererPage', 'updateDataAndSendToServer()', 'Hiding Busy indicator...')
          //               await this.loader.dismissBusyIndicator();
          //             }
          //           }
          //         }, {
          //           text: this.translate.instant('Yes'),
          //           handler: async () => {
          //             // Overwrite with updated submission data, perform save and submit
          //             await this.updateDB(formSubmissionData, pMode);
          //             await this.sendTaskSubmissionToserver(this.updatedTaskSubmission);
          //             await this.sendTaskUserToServer();
          //             await this.unviredSDK.unlockDataSender();
          //             this.fetchData.checkUMPCallDone.next(true);
          //             this.sendAnalystDataToServer();
          //             this.router.navigate(['home']);
          //             if (this.loader.isLoading) {
          //               this.unviredSDK.logInfo('FormRendererPage', 'updateDataAndSendToServer()', 'Hiding Busy indicator...')
          //               await this.loader.dismissBusyIndicator();
          //             }
          //           }
          //         }
          //       ]
          //     });
          //     // Dismiss the loader if present then load alert
          //     if (this.loader.isLoading) {
          //       this.unviredSDK.logInfo('FormRendererPage', 'updateDataAndSendToServer()', 'Hiding Busy indicator...')
          //       await this.loader.dismissBusyIndicator();
          //     }
          //     await alert.present();
          //   }
          // }
        } else {
          this.unviredSDK.logDebug('FormRendererPage', 'updateDataAndSendToServer()', 'Updating DB before network call...')
          await this.updateDB(formSubmissionData, pMode);
          this.unviredSDK.logDebug('FormRendererPage', 'updateDataAndSendToServer()', 'Updating DB Complete. Sending Task Submission....')
          await this.sendTaskSubmissionToserver(this.permitFormSubmissionData);
          // this.unviredSDK.logDebug('FormRendererPage', 'updateDataAndSendToServer()', 'Sending Task Submission Complete. Sending Task User....')
          // await this.sendTaskUserToServer();
          this.unviredSDK.logDebug('FormRendererPage', 'updateDataAndSendToServer()', 'Sending Task User Complete. Unlocking Data sender....')
          await this.unviredSDK.unlockDataSender();
          this.unviredSDK.logDebug('FormRendererPage', 'updateDataAndSendToServer()', 'Unlock Data Sender Complete. Checking whether all calls are complete in order to go back.')
          this.fetchData.checkUMPCallDone.next(true);
          this.sendAnalystDataToServer();
          this.router.navigate(['/permits']);
          if (this.loader.isLoading) {
            this.unviredSDK.logInfo('FormRendererPage', 'updateDataAndSendToServer()', 'Hiding Busy indicator...')
            await this.loader.dismissBusyIndicator();
          }
          this.unviredSDK.logDebug('FormRendererPage', 'updateDataAndSendToServer()', 'Everything is done. Hiding Busy Indicator and going back to previous page.')
        }
      } else {
        await this.loader.showToast(this.translate.instant('Missing submission! try again later.'));
      }
    } catch (error) {
      this.unviredSDK.logError('FormRendererPage', 'updateDataAndSendToServer()', 'ERROR: ' + error);
    }
  }

  async sendTaskUserToServer() {
    // let categoryTaskUserHeader: any = {};
    // let sendTaskUserHeaderToServer: any = {};
    // let taskUserHeader = {} as TASK_USER_HEADER;
    // let dbResponseForTaskUser = {} as DbResult;
    // let serverResponseForTaskUser = {} as SyncResult;
    // this.hash = sha256.create();

    // if (this.taskUser.STATUS !== this.constants.TASK_USER_STATUS.IN_PROGRESS) {
    //   if ((this.completeFlag && this.markComplete) || this.isPrimaryUserCompleted) {
    //     taskUserHeader = this.taskUser;
    //     taskUserHeader.TASK_ID = this.task.TASK_ID;
    //     taskUserHeader.STATUS = this.constants.TASK_USER_STATUS.COMPLETED;
    //     taskUserHeader.COMPLETED_ON = (new Date()).getTime();
    //     taskUserHeader.P_MODE = this.constants.TASK_MODE.P_MODE_M;
    //     taskUserHeader.OBJECT_STATUS = this.constants.OBJECT_STATUS.MODIFY;
    //     taskUserHeader.LOCATION = await this.getCapturedCordinates();
    //     taskUserHeader.SUBM_DATA_HASH = this.hash.update(this.permitFormSubmissionData.DATA).hex();
    //     dbResponseForTaskUser = await this.unviredSDK.dbUpdate(this.constants.TASK_USER_TABLE, taskUserHeader, `TASK_ID = '${taskUserHeader.TASK_ID}' AND USER_ID = '${taskUserHeader.USER_ID}'`);
    //     await this.showResponseWithToaster(dbResponseForTaskUser, this.loader.isLoading);
    //     if (!this.isHybridNative) {
    //       categoryTaskUserHeader[this.constants.TASK_USER_TABLE] = taskUserHeader;
    //       sendTaskUserHeaderToServer[this.constants.TASK_USER_BE] = [categoryTaskUserHeader];

    //       this.unviredSDK.logDebug('FormRendererPage', 'sendTaskUserToServer()', 'Updating Task User.....')
    //       serverResponseForTaskUser = await this.unviredSDK.syncForeground(RequestType.RQST, '', sendTaskUserHeaderToServer, this.constants.PA_UPDATE_TASK_USER, true);
    //       this.unviredSDK.logDebug('FormRendererPage', 'sendTaskUserToServer()', 'Updating Task User. Received Response: ' + JSON.stringify(serverResponseForTaskUser))

    //       await this.serverResponseHandler(serverResponseForTaskUser, this.loader.isLoading);
    //     } else {
    //       serverResponseForTaskUser = await this.unviredSDK.syncBackground(RequestType.RQST, { 'TASK_USER_HEADER': taskUserHeader }, '', this.constants.PA_UPDATE_TASK_USER, this.constants.TASK_USER_BE, taskUserHeader.LID, false);
    //     }
    //   } else {
    //     taskUserHeader = this.taskUser;
    //     taskUserHeader.TASK_ID = this.task.TASK_ID;


    //     if (this.task.TASK_TYPE === this.constants.TASK_TYPE.APPROVAL && this.isRequestApproval) {
    //       taskUserHeader.STATUS = this.constants.TASK_USER_STATUS.IN_REQUEST;
    //       taskUserHeader.P_MODE = this.constants.TASK_MODE.P_MODE_M;
    //     } else if (this.task.TASK_TYPE === this.constants.TASK_TYPE.APPROVAL && this.isSubmitFormViaActionButton) {
    //       taskUserHeader.STATUS = this.constants.TASK_USER_STATUS.COMPLETED;
    //       taskUserHeader.P_MODE = this.constants.TASK_MODE.P_MODE_M;

    //     } else {
    //       taskUserHeader.STATUS = this.constants.TASK_USER_STATUS.IN_PROGRESS;
    //       taskUserHeader.P_MODE = this.constants.TASK_MODE.P_MODE_A;

    //     }

    //     taskUserHeader.OBJECT_STATUS = this.constants.OBJECT_STATUS.MODIFY;
    //     taskUserHeader.SUBM_DATA_HASH = this.hash.update(this.updatedTaskSubmission.FORM_DATA).hex();
    //     taskUserHeader.LOCATION = await this.getCapturedCordinates();
    //     dbResponseForTaskUser = await this.unviredSDK.dbUpdate(this.constants.TASK_USER_TABLE, taskUserHeader, `TASK_ID = '${taskUserHeader.TASK_ID}' AND USER_ID = '${taskUserHeader.USER_ID}'`);
    //     await this.showResponseWithToaster(dbResponseForTaskUser, this.loader.isLoading);
    //     if (!this.isHybridNative) {
    //       categoryTaskUserHeader[this.constants.TASK_USER_TABLE] = taskUserHeader;
    //       sendTaskUserHeaderToServer[this.constants.TASK_USER_BE] = [categoryTaskUserHeader];

    //       this.unviredSDK.logDebug('FormRendererPage', 'sendTaskUserToServer()', 'Updating Task User.....')
    //       serverResponseForTaskUser = await this.unviredSDK.syncForeground(RequestType.RQST, '', sendTaskUserHeaderToServer, this.constants.PA_UPDATE_TASK_USER, true);
    //       this.unviredSDK.logDebug('FormRendererPage', 'sendTaskUserToServer()', 'Updating Task User. Received Response: ' + JSON.stringify(serverResponseForTaskUser))

    //       await this.serverResponseHandler(serverResponseForTaskUser, this.loader.isLoading);
    //     } else {
    //       serverResponseForTaskUser = await this.unviredSDK.syncBackground(RequestType.RQST, { 'TASK_USER_HEADER': taskUserHeader }, '', this.constants.PA_UPDATE_TASK_USER, this.constants.TASK_USER_BE, taskUserHeader.LID, false);
    //     }
    //   }
    // } else if (this.taskUser.STATUS === this.constants.TASK_USER_STATUS.IN_PROGRESS) {
    //   // if (this.completeFlag) {
    //   taskUserHeader = this.taskUser;
    //   taskUserHeader.TASK_ID = this.task.TASK_ID;
    //   if ((this.completeFlag && this.markComplete) || this.isPrimaryUserCompleted) {
    //     taskUserHeader.STATUS = this.constants.TASK_USER_STATUS.COMPLETED;
    //     await this.updateTaskUser(taskUserHeader, dbResponseForTaskUser, categoryTaskUserHeader, sendTaskUserHeaderToServer, serverResponseForTaskUser)
    //   } else if (this.task.TASK_TYPE === this.constants.TASK_TYPE.APPROVAL) {
    //     if (this.isRequestApproval) {
    //       taskUserHeader.STATUS = this.constants.TASK_USER_STATUS.IN_REQUEST;
    //     } else if (this.isSubmitFormViaActionButton) {
    //       taskUserHeader.STATUS = this.constants.TASK_USER_STATUS.COMPLETED;
    //     }
    //     await this.updateTaskUser(taskUserHeader, dbResponseForTaskUser, categoryTaskUserHeader, sendTaskUserHeaderToServer, serverResponseForTaskUser)
    //   }

    // }
  }

  async updateTaskUser(taskUserHeader, dbResponseForTaskUser, categoryTaskUserHeader, sendTaskUserHeaderToServer, serverResponseForTaskUser) {
    // this.hash = sha256.create();
    // taskUserHeader.COMPLETED_ON = (new Date()).getTime();
    // taskUserHeader.P_MODE = this.constants.TASK_MODE.P_MODE_M;
    // taskUserHeader.OBJECT_STATUS = this.constants.OBJECT_STATUS.MODIFY;
    // taskUserHeader.LOCATION = await this.getCapturedCordinates();
    // taskUserHeader.SUBM_DATA_HASH = this.hash.update(this.updatedTaskSubmission.FORM_DATA).hex();
    // dbResponseForTaskUser = await this.unviredSDK.dbUpdate(this.constants.TASK_USER_TABLE, taskUserHeader, `TASK_ID = '${taskUserHeader.TASK_ID}' AND USER_ID = '${taskUserHeader.USER_ID}'`);
    // await this.showResponseWithToaster(dbResponseForTaskUser, this.loader.isLoading);
    // if (!this.isHybridNative) {
    //   categoryTaskUserHeader[this.constants.TASK_USER_TABLE] = taskUserHeader;
    //   sendTaskUserHeaderToServer[this.constants.TASK_USER_BE] = [categoryTaskUserHeader];

    //   this.unviredSDK.logDebug('FormRendererPage', 'sendTaskUserToServer()', 'Updating Task User......')
    //   serverResponseForTaskUser = await this.unviredSDK.syncForeground(RequestType.RQST, '', sendTaskUserHeaderToServer, this.constants.PA_UPDATE_TASK_USER, true);
    //   this.unviredSDK.logDebug('FormRendererPage', 'sendTaskUserToServer()', 'Updating Task User. Received Response: ' + JSON.stringify(serverResponseForTaskUser))

    //   await this.serverResponseHandler(serverResponseForTaskUser, this.loader.isLoading);
    // } else {
    //   serverResponseForTaskUser = await this.unviredSDK.syncBackground(RequestType.RQST, { 'TASK_USER_HEADER': taskUserHeader }, '', this.constants.PA_UPDATE_TASK_USER, this.constants.TASK_USER_BE, taskUserHeader.LID, false);
    // }
  }


  async sendTaskSubmissionToserver(updatedFormSubmission: PERMIT_FORM) {
    let categorySubmissionHeader: any = {};
    let sendSubmissionHeaderToServer: any = {};
    let sendSubmissionHeaderToServerResp = {} as SyncResult;
    // if (this.deletedArray && this.deletedArray.length > 0) {
    //   let that = this;
    //   for (var s5 = 0; s5 < that.deletedArray.length; s5++) {
    //     var attObj = { name: '', originalName: '', size: null, storage: '', type: '', url: '' };
    //     attObj.name = that.deletedArray[s5].TAG3;
    //     attObj.originalName = that.deletedArray[s5].FILE_NAME;
    //     attObj.size = that.deletedArray[s5].TAG4;
    //     attObj.type = that.deletedArray[s5].MIME_TYPE;
    //     attObj.url = that.deletedArray[s5].DATA;
    //     await this.createAttachmentItem(attObj.originalName, attObj.url, attObj.type, this.task.TASK_ID, this.form.FORM_ID, attObj.name, attObj.size, "D", this.deletedArray[s5].UID, that.deletedArray[s5].key);
    //   }
    // }
    // if (this.addedArray && this.addedArray.length > 0) {
    //   for (var s6 = 0; s6 < this.addedArray.length; s6++) {
    //     await this.createAttachmentItem(this.addedArray[s6].originalName, this.addedArray[s6].url, this.addedArray[s6].type, this.task.TASK_ID, this.form.FORM_ID, this.addedArray[s6].name, this.addedArray[s6].size, "A", this.addedArray[s6].uid, this.addedArray[s6].key);
    //   }
    // }
    try {
      if (!this.isHybridNative) {
        let data = [];
        data.push(updatedFormSubmission);
        categorySubmissionHeader[this.constants.PERMIT_HEADER] = this.permitHeaderData
        categorySubmissionHeader[this.constants.PERMIT_FORM] = data;
        sendSubmissionHeaderToServer[AppConstants.BE_PERMIT] = [categorySubmissionHeader];

        this.unviredSDK.logDebug('FormRendererPage', 'sendTaskSubmissionToserver()', 'Updating Task Submission.....')
        sendSubmissionHeaderToServerResp = await this.unviredSDK.syncForeground(RequestType.RQST, '', sendSubmissionHeaderToServer, AppConstants.PA_PERMIT_PA_MODIFY_PERMIT, true);
        this.unviredSDK.logDebug('FormRendererPage', 'sendTaskSubmissionToserver()', 'Updating Task Submission. Received Response: ' + JSON.stringify(sendSubmissionHeaderToServer))

        await this.serverResponseHandler(sendSubmissionHeaderToServerResp, this.loader.isLoading);
        this.shareData.setServerConflictData(sendSubmissionHeaderToServerResp);
      } else {
        // let attachmentquery = `SELECT * FROM ${this.constants.TASK_SUBMISSION_ATTACHMENT_TABLE} WHERE FID = '${updatedFormSubmission.LID}'`;
        sendSubmissionHeaderToServerResp = await this.unviredSDK.syncBackground(RequestType.RQST, { 'PERMIT_FORM': updatedFormSubmission }, '', AppConstants.PA_PERMIT_PA_MODIFY_PERMIT, AppConstants.BE_PERMIT, updatedFormSubmission.LID, false);
        // let fetchAttchments1: DbResult = await this.unviredSDK.dbExecuteStatement(attachmentquery);
      }
    } catch (error:any) {

      if (this.loader.isLoading) {
        this.unviredSDK.logInfo('FormRendererPage', 'sendTaskSubmissionToServer()', 'Hiding Busy indicator...')
        await this.loader.dismissBusyIndicator();
      }
      await this.loader.showAlert('Error', error);
    }
  }

  // Creating attachment for form submission.
  async createAttachmentItem(fileName: string, path: string, fileType: string, taskID: string, formID: string, randomName: string, size: number, pMode: string, uid: string, key: string) {
    // let attachmentObject = {} as TASK_SUBMISSION_ATTACHMENT;
    // let dbResponseForTaskSubmissionAttachment = {} as DbResult;
    // try {

    //   // Create unique file names for new attachment files.
    //   fileName = (pMode == 'D') ? fileName : Math.round(Math.random() * 100000000000) + '_' + fileName;  // Randomize the file name.

    //   attachmentObject.UID = uid;
    //   attachmentObject.FILE_NAME = fileName;
    //   attachmentObject.MIME_TYPE = fileType;
    //   attachmentObject.TAG1 = taskID;
    //   attachmentObject.TAG2 = formID;
    //   attachmentObject.TAG3 = `${this.updatedTaskSubmission.SUBMISSION_ID}~${attachmentObject.UID}`;
    //   attachmentObject.TAG4 = (pMode == 'D') ? String(size) : String(size + '~' + key);
    //   attachmentObject.TAG5 = pMode;
    //   attachmentObject.FID = this.updatedTaskSubmission.LID;
    //   if (!this.isHybridNative) {
    //     attachmentObject.DATA = path;
    //     if (pMode == 'D') {
    //       let deleteResponseFromDB = await this.unviredSDK.dbExecuteStatement(`DELETE FROM ${this.constants.TASK_SUBMISSION_ATTACHMENT_TABLE} WHERE UID = '${uid}'`);
    //       await this.showResponseWithToaster(deleteResponseFromDB, this.loader.isLoading);
    //     }
    //     dbResponseForTaskSubmissionAttachment = await this.unviredSDK.dbInsertOrUpdate(this.constants.TASK_SUBMISSION_ATTACHMENT_TABLE, attachmentObject, false);
    //   } else {
    //     if (pMode == 'D') {
    //       attachmentObject.OBJECT_STATUS = this.constants.OBJECT_STATUS.DELETE;
    //       dbResponseForTaskSubmissionAttachment = await this.unviredSDK.dbInsertOrUpdate(this.constants.TASK_SUBMISSION_ATTACHMENT_TABLE, attachmentObject, false);
    //     } else {
    //       attachmentObject.OBJECT_STATUS = this.constants.OBJECT_STATUS.ADD;
    //       attachmentObject.EXTERNAL_URL = randomName;
    //       await this.writeFile(path, fileName, key);
    //       attachmentObject.LOCAL_PATH = decodeURI((this.devicePlatform === 'windows') ? this.locationPath : this.locationPath.substring(7, this.locationPath.length));
    //       this.unviredSDK.logDebug('FormRendererPage', 'createAttachmentItem()', 'creating attachment: ' + JSON.stringify(attachmentObject, null, 2));
    //       dbResponseForTaskSubmissionAttachment = await this.unviredSDK.createAttachmentItem(this.constants.TASK_SUBMISSION_ATTACHMENT_TABLE, attachmentObject);
    //     }
    //   }
    //   await this.showResponseWithToaster(dbResponseForTaskSubmissionAttachment, this.loader.isLoading);
    //   this.attachments.push(attachmentObject);
    // } catch (error:any) {
    //   if (this.loader.isLoading) {
    //     this.unviredSDK.logInfo('FormRendererPage', 'createAttachmentItem()', 'Hiding Busy indicator...')
    //     await this.loader.dismissBusyIndicator();
    //   }
    //   await this.loader.showAlert('Error', error);
    // }
  }

  async completeFlagChange() {
    let clientCompField = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "client-complete-check-field" }) : [];
    let completeFld = (clientCompField.length > 0 && this.tempData && this.tempData.data !== "" && clientCompField[0].value !== "") ? this.tempData.data[clientCompField[0].value] : true;
    this.markComplete = true;
    this.completeFlag = this.markComplete && completeFld;
    await this.saveForm();
  }

  async writeFile(base64Data: any, fileName: any, key: any) {

    this.unviredSDK.logDebug('FormRendererPage', 'writeFile()', 'Base 64 data length: ' + base64Data.length + ' FileName: ' + fileName);
    let contentType = this.getContentType(base64Data);
    this.unviredSDK.logDebug('FormRendererPage', 'writeFile()', "Content Type: " + contentType);

    let DataBlob = this.base64toBlob(base64Data, contentType);
    let filePath = '';
    let writeFileResp: any = {};
    try {
      switch (true) {
        case (this.platform.is('android')):
          // To make is android 10+ compatibility we are using external application storage
          filePath = this.file.externalApplicationStorageDirectory;  // 'file:///storage/emulated/0/Pictures';
          break;
        case (this.platform.is('ios')):
          filePath = this.file.documentsDirectory;
          break;
        case (this.devicePlatform == 'windows'):
          filePath = 'ms-appdata:///local/';
          break;
      }
      // COMPRESS THE FILE.
      // Convert Blob to file
      var blobFile: any = DataBlob  // new File([DataBlob], fileName);
      blobFile.lastModified = new Date();
      blobFile.name = fileName;
      writeFileResp = await this.file.writeFile(filePath, fileName, blobFile, contentType)
      this.unviredSDK.logDebug('FormRendererPage', 'writeFile()', 'Write file path success: ' + writeFileResp.nativeURL);
      this.locationPath = writeFileResp.nativeURL;

    } catch (error) {
      this.unviredSDK.logError('FormRendererPage', 'writeFile()', 'Error: ' + error)
      this.locationPath = '';
    }
  }

  base64toBlob(b64Data: any, contentType: any) {
    contentType = contentType || '';
    let sliceSize = 512;
    var index = b64Data.indexOf(",");
    var strImage = b64Data.substring(index + 1);
    let byteCharacters = atob(strImage); // this.utilityFunction.decodeUnicode(strImage);
    let byteArrays = [];
    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      let slice = byteCharacters.slice(offset, offset + sliceSize);
      let byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      var byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }
    let blob = new Blob(byteArrays, {
      type: contentType
    });
    return blob;
  }

  getContentType(base64Data: any) {
    let block = base64Data.split(";");
    let contentType = block[0].split(":")[1];
    return contentType;
  }

  // Setting master data to all dropdowns in form.
  async getFormioMasterDataObjects(obj: any, key: any, val: any) {
    var objects = [];

    for (var i in obj) {
      if (typeof obj[i] == 'object') {
        objects = objects.concat(this.getFormioMasterDataObjects(obj[i], key, val));
      } else if (i == key && obj[key] == val) {
        var val1 = obj['valueProperty'];
        var val2 = val1.indexOf(".");
        var ress = val1.slice(0, val2);
        var obj1 = [];

        if (this.isHybridNative) {
          let masterdataResourcequery = `SELECT * FROM ${this.constants.MASTER_DATA_TABLE} WHERE RESOURCE_NAME = '${ress}'`;
          let masterdataResults: DbResult = await this.unviredSDK.dbExecuteStatement(masterdataResourcequery);
          if (masterdataResults.type == 0) {
            obj1 = masterdataResults.data;
          }
        } else {
          var resource = this.masterData.MASTER_DATA;
          if (resource != undefined && resource != "") {
            for (var x = 0; x < resource.length; x++) {
              var s = resource[x].MASTER_DATA_HEADER.RESOURCE_NAME;
              if (s == ress) {
                obj1.push(JSON.parse(resource[x].MASTER_DATA_HEADER.DATA));
              }
            }
          }
        }

        if (obj.data && typeof obj.data == 'object' && obj.data != undefined) {
          if (!obj.data['json']) {
            obj.masterdata = obj1;
            var tmplt = obj['template'];
            if (tmplt != "") {
              var str1 = tmplt.substring(tmplt.lastIndexOf('.') + 1, tmplt.length);
              var str2 = str1.substring(0, str1.indexOf('}'), str1.length);
              var str3 = "<span>{{" + "item." + str2.trim() + "}}</span>";
              obj['template'] = str3;
              obj.idProperty = str2.trim();
            }
            var val = obj['valueProperty'];
            if (val != "") {
              var valProp = val.substring(val.lastIndexOf('.') + 1, val.length);
              obj['valueProperty'] = valProp;
            }
          } else {
            obj.masterdata = obj.data['json'];
          }
        } else if (!obj.data) {
          obj['data'] = {
            "values": [],
            "json": "",
            "url": "",
            "resource": "",
            "custom": ""
          };
          obj.masterdata = obj1;
          var tmplt = obj['template'];
          if (tmplt != "") {
            var str1 = tmplt.substring(tmplt.lastIndexOf('.') + 1, tmplt.length);
            var str2 = str1.substring(0, str1.indexOf('}'), str1.length);
            var str3 = "<span>{{" + "item." + str2.trim() + "}}</span>";
            obj['template'] = str3;
            obj.idProperty = str2.trim();
          }
          var val = obj['valueProperty'];
          if (val != "") {
            var valProp = val.substring(val.lastIndexOf('.') + 1, val.length);
            obj['valueProperty'] = valProp;
          }
        }
      }
    }
    return obj;
  }

  async showFormInformation() {
    // const formInformation = await this.setFormInformation(this.task.COMMENTS, this.task.PRIORITY, this.task.TASK_DUEDATE, this.task.LAST_SUBMITTED_ON);
    // let username: string = await this.appSpecificUtility.getUserName(this.taskSubmission.LAST_UPDATED_BY);
    // const popover = await this.popoverController.create({
    //   component: FormInfoPopupPage,
    //   componentProps: {
    //     name: this.form.FORM_DESC,
    //     duedate: formInformation.DUEDATE,
    //     priority: formInformation.TASK_PRIORITY,
    //     comments: formInformation.TASK_COMMENTS,
    //     submittedTime: formInformation.LAST_SUBMITTED_ON,
    //     description: this.form.FORM_DESC,
    //     version: this.form.VERSION,
    //     createdOn: this.task.CREATED_ON,
    //     upadtedBy: username
    //   },
    //   translucent: true,
    //   animated: true,
    //   backdropDismiss: false,
    //   showBackdrop: true,
    //   keyboardClose: true,
    //   cssClass: 'formInfoPopup',
    //   enterAnimation: this.loader.enterAnimationPopover,
    //   leaveAnimation: this.loader.leaveAnimation,
    // });
    // await popover.present();
  }

  // Check Task priority and Task duedate is provided
  async setFormInformation(taskComments: string, taskPriority: number, taskDuedate: string, submittedTime: number) {
    let taskInfo = { TASK_COMMENTS: '', DUEDATE: '', TASK_PRIORITY: '', LAST_SUBMITTED_ON: submittedTime };
    if (taskComments) {
      if ((taskComments === '') || (taskComments.trim().toLowerCase() === this.task.TASK_DESC.trim().toLowerCase())) {
        taskInfo.TASK_COMMENTS = this.translate.instant("No Comments");
      } else {
        taskInfo.TASK_COMMENTS = taskComments;
      }
    } else {
      taskInfo.TASK_COMMENTS = this.translate.instant("No Comments");
    }
    let fetchPriority = {} as DbResult;
    let priorityDB: PRIORITY_HEADER[];
    let priorityHeader: PRIORITY_HEADER;
    let priorityLabel: string;
    switch (true) {
      case (taskPriority && (taskDuedate && taskDuedate.trim().length > 0)):
        if (taskPriority !== null || taskPriority !== undefined) {
          fetchPriority = await this.unviredSDK.dbExecuteStatement(`SELECT PRIORITY_NAME FROM ${this.constants.PRIORITY_TABLE} WHERE PRIORITY_VALUE = '${taskPriority}'`);
          if (fetchPriority.type === ResultType.success) {
            if (fetchPriority.data && fetchPriority.data.length > 0) {
              priorityDB = fetchPriority.data;
              priorityHeader = priorityDB[0];
              priorityLabel = `${priorityHeader.PRIORITY_NAME} Priority`;
            }
          } else {
            priorityLabel = this.translate.instant("Priority not found");
          }
        } else {
          priorityLabel = this.translate.instant("Priority not found");
        }
        taskInfo.TASK_PRIORITY = priorityLabel;
        taskInfo.DUEDATE = this.utilityFunction.formatStringBYDateString(taskDuedate);
        break;
      case (taskPriority && (!taskDuedate || taskDuedate.trim().length === 0)):
        if (taskPriority !== null || taskPriority !== undefined) {
          fetchPriority = await this.unviredSDK.dbExecuteStatement(`SELECT PRIORITY_NAME FROM ${this.constants.PRIORITY_TABLE} WHERE PRIORITY_VALUE = '${taskPriority}'`);
          if (fetchPriority.type === ResultType.success) {
            if (fetchPriority.data && fetchPriority.data.length > 0) {
              priorityDB = fetchPriority.data;
              priorityHeader = priorityDB[0];
              priorityLabel = `${priorityHeader.PRIORITY_NAME} Priority`;
            }
          } else {
            priorityLabel = this.translate.instant("Priority not found");
          }
        } else {
          priorityLabel = this.translate.instant("Priority not found");
        }
        taskInfo.TASK_PRIORITY = priorityLabel;
        taskInfo.DUEDATE = this.translate.instant("No Duedate");
        break;
      case (!taskPriority && (taskDuedate && taskDuedate.trim().length > 0)):
        taskInfo.TASK_PRIORITY = this.translate.instant("No Priority");
        taskInfo.DUEDATE = this.utilityFunction.formatStringBYDateString(taskDuedate);
        break;
      case (!taskPriority && (!taskDuedate || taskDuedate.trim().length === 0)):
        taskInfo.TASK_PRIORITY = this.translate.instant("No Priority");
        taskInfo.DUEDATE = this.translate.instant("No Duedate");
        break;
    }
    return taskInfo;
  }

  async primaryUserValidationAlertForApproval(msg: string) {

    let promptOnFormSave: boolean = false;
    if (!this.markComplete) {
      let promptOnFormSaveData = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "prompt-on-form-save" }) : [];
      if (promptOnFormSaveData && promptOnFormSaveData.length > 0 && promptOnFormSaveData[0]) {
        promptOnFormSave = promptOnFormSaveData[0].value
        if (promptOnFormSave) {
          let msgFormSave = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "message-form-save" }) : [];
          if (msgFormSave && msgFormSave.length > 0 && msgFormSave[0]) {
            msg = msgFormSave[0].value;
          }
        }

      }
    }
    if (promptOnFormSave) {
      const primaryUserValidationAlert = await this.alertController.create({
        header: this.translate.instant('Confirmation'),
        message: msg,
        animated: true,
        backdropDismiss: false,
        buttons: [
          {
            text: this.translate.instant('No'),
            role: 'cancel',
            handler: () => { }
          }, {
            text: this.translate.instant('Yes'),
            handler: async () => { await this.draftForm('yes'); }
          }
        ]
      });
      await primaryUserValidationAlert.present();
      if (this.loader.isLoading) {
        this.unviredSDK.logInfo('FormRendererPage', 'primaryUserValidationAlert()', 'Hiding Busy indicator...')
        await this.loader.dismissBusyIndicator();
      }
    } else {
      await this.draftForm('yes')
    }
  }

  async primaryUserValidationAlert(msg: string) {

    const primaryUserValidationAlert = await this.alertController.create({
      header: this.translate.instant('Error'),
      message: msg,
      animated: true,
      backdropDismiss: false,
      buttons: [this.translate.instant('OK')]
    });
    await primaryUserValidationAlert.present();
    if (this.loader.isLoading) {
      this.unviredSDK.logInfo('FormRendererPage', 'primaryUserValidationAlert()', 'Hiding Busy indicator...')
      await this.loader.dismissBusyIndicator();
    }

  }

  // Error/Warning Alert for user
  async completionAlert(msgType: string, msg: string) {
    const alert = await this.alertController.create({
      header: msgType,
      subHeader: msg,
      animated: true,
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant('OK'),
          handler: async () => {
            await this.unviredSDK.unlockDataSender();
            this.fetchData.checkUMPCallDone.next(true);
            this.sendAnalystDataToServer();
            this.router.navigate(['/permits']);
          }
        }
      ]
    });
    if (this.loader.isLoading) {
      this.unviredSDK.logInfo('FormRendererPage', 'completionAlert()', 'Hiding Busy indicator...')
      await this.loader.dismissBusyIndicator();
    }
    await alert.present();
  }

  async confirmationAlert() {
    // Partial Save event 
    await this.getPartialFilledData();
    this.loadFormAnalystData({ "event": "PARTIAL", "fieldName": "", "sequenceNumber": this.seqEve++, "timestamp": new Date().getTime(), "additionalData": this.additionalDataEve });

    if (this.markComplete) {
      // Complete
      if (this.taskUser.USER_ID === this.task.PRIMARY_USER) {
        // for primary user
        let messages = this.translate.instant('Form is partially filled. Still mark complete?');
        const alert = await this.alertController.create({
          header: this.translate.instant('Confirmation'),
          message: messages,
          animated: true,
          backdropDismiss: false,
          buttons: [
            {
              text: this.translate.instant('No'),
              role: 'cancel',
              handler: () => { }
            }, {
              text: this.translate.instant('Yes'),
              handler: async () => {
                // logic to complete form
                if (!this.loader.isLoading) {
                  await this.loader.showBusyIndicator(this.translate.instant('Completing Form...'), 'crescent');
                }
                this.isPrimaryUserCompleted = true
                let val = await returnTempData();
                let tempData = val.tempData ? val.tempData : val.submission;
                await this.updateDataAndSendToServer(tempData, this.constants.TASK_MODE.P_MODE_M);
                this.isPrimaryUserCompleted = false
              }
            }
          ]
        });
        if (this.loader.isLoading) {
          this.unviredSDK.logInfo('FormRendererPage', 'confirmationAlert()', 'Hiding Busy indicator...')
          await this.loader.dismissBusyIndicator();
        }
        await alert.present();
      } else {
        // for other than primary user
        let messages = this.translate.instant('Form cannot be completed until all required fields are entered. Save instead?');
        const alert = await this.alertController.create({
          header: this.translate.instant('Confirmation'),
          message: messages,
          animated: true,
          backdropDismiss: false,
          buttons: [
            {
              text: this.translate.instant('No'),
              role: 'cancel',
              handler: () => { }
            }, {
              text: this.translate.instant('Yes'),
              handler: async () => { await this.draftForm('yes'); }
            }
          ]
        });
        if (this.loader.isLoading) {
          this.unviredSDK.logInfo('FormRendererPage', 'confirmationAlert()', 'Hiding Busy indicator...')
          await this.loader.dismissBusyIndicator();
        }
        await alert.present();
      }

    } else {
      // Save
      // show promt when prompt-on-form-save is configured else save form
      let promptOnFormSave: boolean = false;
      let msg = "";
      if (!this.markComplete) {
        let promptOnFormSaveData = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "prompt-on-form-save" }) : [];
        if (promptOnFormSaveData && promptOnFormSaveData.length > 0 && promptOnFormSaveData[0]) {
          promptOnFormSave = promptOnFormSaveData[0].value
          if (promptOnFormSave) {
            let msgFormSave = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "message-form-save" }) : [];
            if (msgFormSave && msgFormSave.length > 0 && msgFormSave[0]) {
              msg = msgFormSave[0].value;
            }
          }
        }
      }
      if (promptOnFormSave) {
        let messages = ""
        if (msg && msg !== "" && msg.length > 0) {
          messages = msg;
        }
        // let completeFld = false;
        // let clientCompField = this.attributesJson ? this.attributesJson.filter(e => { return e.key.trim() == "client-complete-check-field" }) : [];
        // if (clientCompField.length > 0 && this.tempData && this.tempData.data && clientCompField[0].value !== "") {
        //   completeFld = this.tempData.data[clientCompField[0].value];
        // } else {
        //   if(this.calculatedPercentage === 100){
        //   await this.draftForm('yes');
        //   return;
        //   }
        // }

        // if (completeFld && this.calculatedPercentage === 100) {
        if (this.calculatedPercentage === 100) {
          await this.draftForm('yes')
        } else {
          const alert = await this.alertController.create({
            header: this.translate.instant('Confirmation'),
            message: messages,
            animated: true,
            backdropDismiss: false,
            buttons: [
              {
                text: this.translate.instant('No'),
                role: 'cancel',
                handler: () => { }
              }, {
                text: this.translate.instant('Yes'),
                handler: async () => { await this.draftForm('yes'); }
              }
            ]
          });
          if (this.loader.isLoading) {
            this.unviredSDK.logInfo('FormRendererPage', 'confirmationAlert()', 'Hiding Busy indicator...')
            await this.loader.dismissBusyIndicator();
          }
          await alert.present();
        }

      } else {
        await this.draftForm('yes')
      }
    }
  }

  async goBackToLastLocation() {
    let isFormDataChanged: boolean = false;
    try {
      /**
       * Busy indicator loads indefinetly on save.
       */
      let lastFormData = await returnLastTempData();

      let lastChanges = JSON.stringify(lastFormData.tempData.data);
      let hashValueWithLastChanges = hash(lastChanges);

      let data = localStorage.getItem("initialFormData");
      let hashValueWithInitialData = hash(data);
      let diff = hashValueWithInitialData.localeCompare(hashValueWithLastChanges);
      isFormDataChanged = (diff === 0) ? false : true;

    } catch (error) {
      this.unviredSDK.logError('FormRendererPage', 'returnLastTempData()', 'ERROR: ' + error);
    }
    if (isFormDataChanged && (!this.formReadOnlyFlag)) {
      // CHANGE IN DATA
      let looseDataAlert;
      if (this.task.TASK_TYPE !== this.constants.TASK_TYPE.APPROVAL) {
        looseDataAlert = await this.alertController.create({
          header: this.translate.instant('Warning'),
          message: this.translate.instant(`Would you like to :`),
          animated: true,
          backdropDismiss: true,
          mode: "md",
          buttons: [
            {
              text: this.translate.instant('Discard Changes'),
              handler: async () => {
                localStorage.setItem('renderBackFormDataChanged', 'false')
                isFormDataChanged = false;
                this.markComplete = false;
                this.loadFormAnalystData({ "event": "CANCEL", "fieldName": "", "timestamp": new Date().getTime(), "sequenceNumber": this.seqEve++, "additionalData": "" });
                if (!this.loader.isLoading) {
                  await this.loader.showBusyIndicator(this.translate.instant('Discarding changes...'), 'crescent');
                }
                await this.unviredSDK.unlockDataSender();
                if (this.createFlag) { // Only on create and navigate back reload taskslist
                  this.fetchData.checkUMPCallDone.next(true);
                }
                this.sendAnalystDataToServer();
                this.router.navigate(['/permits']);
                if (this.loader.isLoading) {
                  this.unviredSDK.logInfo('FormRendererPage', 'goBackToLastLocation()', 'Hiding Busy indicator...')
                  await this.loader.dismissBusyIndicator();
                }
              }
            },
            {
              text: this.translate.instant('Save'),
              handler: async () => {
                isFormDataChanged = false;
                this.markComplete = false;
                await this.saveForm();
                if (this.loader.isLoading) {
                  this.unviredSDK.logInfo('FormRendererPage', 'goBackToLastLocation()', 'Hiding Busy indicator...')
                  await this.loader.dismissBusyIndicator();
                }
              }
            },
            {
              text: this.translate.instant('Complete'),
              handler: async (data) => {
                isFormDataChanged = false;
                await this.completeFlagChange();
                if (this.loader.isLoading) {
                  this.unviredSDK.logInfo('FormRendererPage', 'goBackToLastLocation()', 'Hiding Busy indicator...')
                  await this.loader.dismissBusyIndicator();
                }
              }
            }
          ]
        });
      } else {
        looseDataAlert = await this.alertController.create({
          header: this.translate.instant('Warning'),
          message: this.translate.instant(`Would you like to :`),
          animated: true,
          backdropDismiss: true,
          mode: "md",
          buttons: [
            {
              text: this.translate.instant('Discard Changes'),
              handler: async () => {
                isFormDataChanged = false;
                this.loadFormAnalystData({ "event": "CANCEL", "fieldName": "", "timestamp": new Date().getTime(), "sequenceNumber": this.seqEve++, "additionalData": "" });
                if (!this.loader.isLoading) {
                  await this.loader.showBusyIndicator(this.translate.instant('Discarding changes...'), 'crescent');
                }
                await this.unviredSDK.unlockDataSender();
                if (this.createFlag) { // Only on create and navigate back reload taskslist
                  this.fetchData.checkUMPCallDone.next(true);
                }
                this.sendAnalystDataToServer();
                this.router.navigate(['/permits']);
                if (this.loader.isLoading) {
                  this.unviredSDK.logInfo('FormRendererPage', 'goBackToLastLocation()', 'Hiding Busy indicator...')
                  await this.loader.dismissBusyIndicator();
                }
              }
            },
            {
              text: this.translate.instant('Cancel'),
              handler: async () => {
                isFormDataChanged = false;
                if (this.loader.isLoading) {
                  this.unviredSDK.logInfo('FormRendererPage', 'goBackToLastLocation()', 'Hiding Busy indicator...')
                  await this.loader.dismissBusyIndicator();
                }
              }
            },
          ]
        });
      }
      await looseDataAlert.present().then(() => {
        var btns = document.querySelectorAll('ion-alert div.alert-button-group button.alert-button.sc-ion-alert-md');
        if (!this.isHybridNative) {
          if (this.task.TASK_TYPE !== this.constants.TASK_TYPE.APPROVAL) {
            if (document.querySelector('ion-alert div.alert-wrapper.sc-ion-alert-md')) {
              document.querySelector('ion-alert div.alert-wrapper.sc-ion-alert-md').setAttribute('style', 'width: 450px !important; max-width: 100% !important; align-items: center;');
            }
          } else {
            if (document.querySelector('ion-alert div.alert-wrapper.sc-ion-alert-md')) {
              document.querySelector('ion-alert div.alert-wrapper.sc-ion-alert-md').setAttribute('style', 'max-width: 100% !important; align-items: center;');
            }
            document.querySelector('ion-alert div.alert-button-group').setAttribute('style', 'justify-content:center;');

          }
          let btnGrpVertical = document.querySelector('ion-alert div.alert-button-group-vertical.sc-ion-alert-md');
          if (btnGrpVertical) {
            btnGrpVertical.setAttribute('style', '-ms-flex-direction: row !important; flex-direction: row !important; display: flex; flex-wrap: nowrap;justify-content: center;')
          }
          btns.forEach(btn => {
            btn.setAttribute('style', 'outline: auto; margin: 2%; text-transform: none !important; background-color: var(--ion-color-primary); color: white; text-align: center;');
          });
        } else {
          if (document.querySelector('ion-alert div.alert-wrapper.sc-ion-alert-md')) {
            document.querySelector('ion-alert div.alert-wrapper.sc-ion-alert-md').setAttribute('style', 'align-items: center;opacity: 1;');
          }
          document.querySelector('ion-alert div.alert-button-group').setAttribute('style', 'align-items: flex-end;');
          btns.forEach(btn => {
            btn.setAttribute('style', 'outline: auto; margin: 0% 0% 2% 0%; text-transform: none !important; width: inherit; background-color: var(--ion-color-primary); color: white;');
            btn.firstElementChild.setAttribute('style', 'justify-content: center !important; -webkit-box-pack: center; -ms-flex-pack: center;');
          });
        }
      });
    } else {
      // NO CHANGE IN DATA
      localStorage.setItem('renderBackFormDataChanged', 'false')
      this.loadFormAnalystData({ "event": "CANCEL", "fieldName": "", "timestamp": new Date().getTime(), "sequenceNumber": this.seqEve++, "additionalData": "" });
      if (!this.loader.isLoading) {
        await this.loader.showBusyIndicator(this.translate.instant('Discarding changes...'), 'crescent');
      }
      await this.unviredSDK.unlockDataSender();
      if (this.createFlag) { // Only on create and navigate back reload taskslist
        this.fetchData.checkUMPCallDone.next(true);
      }
      this.sendAnalystDataToServer();
      this.router.navigate(['/permits']);
      if (this.loader.isLoading) {
        this.unviredSDK.logInfo('FormRendererPage', 'goBackToLastLocation()', 'Hiding Busy indicator...')
        await this.loader.dismissBusyIndicator();
      }
    }
  }

  ionViewDidLeave() {
    // Remove Event listeners to avoid multiple firing
    document.removeEventListener('onFormOpen', this.onFormOpenListener);
    document.removeEventListener('onFocus', this.onFocusListener);
    document.removeEventListener('onBlur', this.onBlurListener);
    document.removeEventListener('onChange', this.onChangeListener);
    document.removeEventListener('onError', this.onErrorListener);

    if (this.cssSettings.length > 0 && this.cssSettings[0].SETTINGS_VALUE != "") {
      if (!this.platform.is('desktop') && this.platform.is('mobile')) {
        $("style.customCss").remove();
      } else {
        $('link[href="' + this.cssSettings[0].SETTINGS_VALUE + '"]').remove();
      }
    }
    if (this.cameraOpen) {
      this.cameraOpen = false;
    } else {
      this.shareData.resetFormData();
    }
    document.removeEventListener('displaySDCWorkflowError', this.displaySDCWorkflowErrorHandler.bind(this), true)
    document.removeEventListener('submitFormViaActionButton', this.submitFormViaActionButtonListener);

  }

  displaySDCWorkflowErrorHandler(event: any) {
    this.loader.showWorkFlowToast(event.detail.errorMsg);
  }

  // Handle response from server during foreground call
  async serverResponseHandler(response: SyncResult, busyindicator?: boolean) {
    if (response.code && response.code === 401) {
      if (busyindicator) {
        this.unviredSDK.logInfo('FormRendererPage', 'serverResponseHandler()', 'Hiding Busy indicator...')
        await this.loader.dismissBusyIndicator();
      }
      this.appSpecificUtility.performLogoutOperation();
    } else {
      switch (response.type) {
        case ResultType.success:
          await this.showResponseWithToaster(response, busyindicator);
          break;
        case ResultType.error:
          await this.showResponseWithToaster(response, busyindicator);
          break;
      }
    }
  }

  // Show message or error if any
  async showResponseWithToaster(response: any, busyindicator?: boolean) {
    if (response.message && response.message.trim().length !== 0) {
      if (busyindicator) {
        this.unviredSDK.logInfo('FormRendererPage', 'showResponseWithToaster()', 'Hiding Busy indicator...')
        await this.loader.dismissBusyIndicator();
      }
      this.loader.showToast(response.message);
    } else if (response.error && response.error.trim().length !== 0) {
      if (busyindicator) {
        this.unviredSDK.logInfo('FormRendererPage', 'showResponseWithToaster()', 'Hiding Busy indicator...')
        await this.loader.dismissBusyIndicator();
      }
      this.toastr.error('', response.error, { timeOut: 2000 });
    }
  }

  helpFormRendererInfo() {
    let url = 'https://docs.unvired.com/formsapp/formfill';
    if (this.isHybridNative) {
      let browser = this.iab.create(url, '_system', 'location=no,usewkwebview=true');
      browser.show();
    } else {
      window.open(url, '_blank');
    }
  }

  // Fetch task alert count
  async fetchFormAlertCount() {
    // let queryToFetchAlertCount = `SELECT COUNT(*) AS ALERT_COUNT FROM ${this.constants.TASK_ALERT_TABLE} WHERE TASK_ID = '${this.task.TASK_ID}' AND FORM_ID = '${this.form.FORM_ID}' AND USER_ID = '${this.taskUser.USER_ID}'`;
    // let fetchAlertDB: DbResult = await this.unviredSDK.dbExecuteStatement(queryToFetchAlertCount);
    // if (fetchAlertDB.type === ResultType.success) {
    //   if (fetchAlertDB && fetchAlertDB.data && fetchAlertDB.data.length > 0 && fetchAlertDB.data[0] && fetchAlertDB.data[0].ALERT_COUNT) {
    //     this.alertCount = fetchAlertDB.data[0].ALERT_COUNT;
    //   } else {
    //     this.alertCount = 0;
    //   }
    // } else {
    //   this.alertCount = 0;
    // }
    return this.alertCount;
  }

  async fetchFormUnReadAlertCount() {
    // this.unReadCount = 0;
    // let queryToFetchAlertCount = `SELECT * FROM ${this.constants.TASK_ALERT_TABLE} WHERE TASK_ID = '${this.task.TASK_ID}' AND FORM_ID = '${this.form.FORM_ID}' AND USER_ID = '${this.taskUser.USER_ID}'`;
    // let fetchAlertDB: DbResult = await this.unviredSDK.dbExecuteStatement(queryToFetchAlertCount);
    // if (fetchAlertDB.type === ResultType.success) {
    //   for (let i = 0; i < fetchAlertDB.data.length; i++) {
    //     if (fetchAlertDB.data[i].ALERT_READ == null || fetchAlertDB.data[i].ALERT_READ == 0) {
    //       this.unReadCount++;
    //       this.showUnreadAlerts = true;
    //     }
    //   }
    // } else {
    //   this.unReadCount = 0;
    // }
  }
  //Show annotation review comments if present
  async fetchReviewComments() {
    // let queryToFetchReviews = `SELECT * FROM ${this.constants.TASK_ANNOTATIONS_TABLE} WHERE SUBMISSION_ID = '${this.taskSubmission.SUBMISSION_ID}'`;
    // let fetchReview: DbResult = await this.unviredSDK.dbExecuteStatement(queryToFetchReviews);
    // if (fetchReview.type === ResultType.success) {
    //   if (fetchReview.data.length > 0 && !this.annotationEnd) {
    //     this.showReviewComments = true;
    //   }
    // }
  }

  // Check alert details for task
  async openAlertModal() {
  //   this.showUnreadAlerts = false;
  //   const taskAlertModal = await this.modalController.create({
  //     component: TaskAlertPage,
  //     componentProps: { taskDesc: this.form.FORM_DESC, taskID: this.task.TASK_ID, userID: this.taskUser.USER_ID },
  //     animated: true,
  //     showBackdrop: true,
  //     backdropDismiss: false,
  //     enterAnimation: this.loader.enterAnimation,
  //     leaveAnimation: this.loader.leaveAnimation
  //   });
  //   await taskAlertModal.present();
  //   let data = await taskAlertModal.onDidDismiss();
  //   if (data != undefined) {
  //     await this.ionViewDidEnter();
  //   }
  }

  // Adding form submission data file component files
  modifySubmissionObject(obj: any, key: string, val: string) {
    var objects = [];
    for (var i in obj) {
      if (!obj.hasOwnProperty(i)) { continue; }
      if (typeof obj[i] == 'object') {
        if (i === key) {
          for (let att1 = 0; att1 < this.fileArray.length; att1++) {
            if (this.fileArray[att1].key === key) {
              delete this.fileArray[att1]['key'];
              obj[i].push(this.fileArray[att1]);
            }
          }
        }
        objects = objects.concat(this.modifySubmissionObject(obj[i], key, val));
      }
    }
    return obj;
  }

  destroyScannerComponent() {
    this.setBarcode = !this.setBarcode;
    this.cameraOpen = !this.cameraOpen;
    this.shareData.getBackToRenderer.next('');
  }

  // async openDocumentsModal() {
  //   const docModal = await this.modalController.create({
  //     component: FormDocumentsPage,
  //     componentProps: { docUIDs: this.form.DOCUMENTS },
  //     animated: true,
  //     showBackdrop: true,
  //     backdropDismiss: false,
  //     enterAnimation: this.loader.enterAnimation,
  //     leaveAnimation: this.loader.leaveAnimation
  //   });
  //   await docModal.present();
  // }

  // save and caret down should show a popover
  async presentPopover(ev: any) {
    const optionPopover = await this.popoverCtrl.create({
      component: OptionsPopoverPage,
      event: ev,
      animated: true,
      showBackdrop: true,
      backdropDismiss: true,
      translucent: true,
      cssClass: 'renderOptions',
      componentProps: { optionType: "renderer", alertBadge: this.showAlertBadge, count: this.unReadCount, readOnly: this.formReadOnlyFlag,  form: this.form, formReadWriteFlag: this.formReadWriteFlag, percentageCompleted: this.calculatedPercentage, formData: this.tempData, isHybridNative: this.isHybridNative },
      // componentProps: { optionType: "renderer", alertBadge: this.showAlertBadge, count: this.unReadCount, showDocs: this.showFormDocs, readOnly: this.formReadOnlyFlag, shiftType: this.isTaskTypeShift, taskType: this.taskUser.STATUS, form: this.form, taskHeaderType: this.task.TASK_TYPE, formReadWriteFlag: this.formReadWriteFlag, percentageCompleted: this.calculatedPercentage, formData: this.tempData, isHybridNative: this.isHybridNative },
    });
    await optionPopover.present();
    const { data } = await optionPopover.onDidDismiss();
    if (data !== undefined) {
      switch (data) {
        case this.translate.instant("Complete"):
          this.completeFlagChange();
          break;
        case this.translate.instant("Comments"):
          this.checkReviewOrComments();
          break;
        case this.translate.instant("Info"):
          this.showFormInformation();
          break;
        // case this.translate.instant("Alert"):
        //   this.openAlertModal();
        //   break;
        case this.translate.instant("Document"):
          // this.openDocumentsModal();
          break;
        case this.translate.instant("Help"):
          this.helpFormRendererInfo();
          break;
        case this.translate.instant("Request Approval"):
          this.isRequestApproval = true;
          this.markComplete = false;
          await this.saveForm();
          break;
        case this.translate.instant('History'):
          // this.openFormStatus(this.task);
          break;
        case this.translate.instant("Save"):
          this.markComplete = false;
          await this.saveForm();
          break;
      }
    }
  }

  async saveFormRequestApproval() {
    this.markComplete = false;
    this.isRequestApproval = true;
    await this.saveForm();
  }

  // async openFormStatus(task: any) {
  //   // Check the network connection
  //   if (this.networkConection.getCurrentNetworkStatus() === ConnectionStatus.Offline) {
  //     await this.loader.showToast(this.translate.instant("You need to be online to access history"));
  //   } else {
  //     const formStatusModal = await this.modalController.create({
  //       component: FormStatusPage,
  //       componentProps: { task: task, userID: this.taskUser.USER_ID },
  //       cssClass: 'my-custom-modal-css',
  //       animated: true,
  //       showBackdrop: true,
  //       backdropDismiss: false,
  //       enterAnimation: this.loader.enterAnimation,
  //       leaveAnimation: this.loader.leaveAnimation
  //     });
  //     await formStatusModal.present();
  //   }
  // }

  // navigate to annotation page.
  async checkReviewOrComments() {
    this.showReviewComments = false;
    this.loadFormAnalystData({ "event": "ANN_START", "fieldName": "", "timestamp": new Date().getTime(), "sequenceNumber": this.seqEve++, "additionalData": "" });
    this.router.navigate(['form-annotation']);
  }

  // initialisePercentageLoader(val) {
  //   // Element inside which you want to see the chart
  //   let element = document.querySelector('#gaugeArea');
  //   if (element) {
  //     element.innerHTML = "";
  //   }
  //   let arcDelimit = 0.1;
  //   if (this.calculatedPercentage == 0) {
  //     arcDelimit = 1;
  //   } else if (this.calculatedPercentage == 100) {
  //     arcDelimit = 99.99;
  //   } else {
  //     arcDelimit = this.calculatedPercentage;
  //   }

  //   // Properties of the gauge
  //   let gaugeOptions = {
  //     hasNeedle: true,
  //     outerNeedle: false,
  //     needleColor: 'black',
  //     needleUpdateSpeed: 1000,
  //     needleStartValue: val,
  //     arcColors: ["rgb(61,204,91)", "rgb(255,84,84)"],
  //     arcDelimiters: [arcDelimit],
  //     rangeLabel: ['0%', '100%'],
  //     centralLabel: this.calculatedPercentage + '%',
  //   }
  //   // Drawing and updating the chart
  //   GaugeChart.gaugeChart(element, 200, gaugeOptions).updateNeedle(this.calculatedPercentage);
  // }

  // enableGaugeChart() {
  //   this.showChart = true;
  //   setTimeout(() => {
  //     this.initialisePercentageLoader(this.calculatedPercentage);
  //   }, 100);
  // }

  dismissGauge() {
    this.showChart = false;
  }

  // Event Listeners function calls for Form Event Analytics
  onFormOpenListener(event: any) {
    const data = event.detail;
    data.sequenceNumber = that.seqEve++;
    that.insertFormEveAnalyst(data);
  }

  onFocusListener(event: any) {
    const data = event.detail;
    data.sequenceNumber = that.seqEve++;
    that.loadFormAnalystData(data);
  }

  onBlurListener(event: any) {
    const data = event.detail;
    data.sequenceNumber = that.seqEve++;
    that.loadFormAnalystData(data);
  }

  onAnnotateEndListener(event: any) {
    that.annotationEnd = true;
    that.showReviewComments = false;
    const data = event.detail;
    data.sequenceNumber = that.seqEve++;
    that.loadFormAnalystData(data);
  }

  onChangeListener(event: any) {
    const data = event.detail;
    data.sequenceNumber = that.seqEve++;
    that.loadFormAnalystData(data);
  }

  onErrorListener(event: any) {
    const data = event.detail;
    data.sequenceNumber = that.seqEve++;
    that.loadFormAnalystData(data);
  }
  async getPartialFilledData() {
    // Get form data when form filled partially
    try {
      let partialFilledData = await returnTempData();
      let arr = [];
      let keys = Object.keys(partialFilledData.tempData.data);
      Object.values(partialFilledData.tempData.data).forEach((ele, i) => {
        if (ele === "") {
          arr.push(keys[i]);
        }
      });
      this.additionalDataEve = JSON.stringify(arr);
    } catch (error) {
      this.unviredSDK.logError('FormRendererPage', 'getPartialFilledData()', 'ERROR: ' + error);
    }
  }

  async loadFormAnalystData(data) {
    // let analyticsData = data.additionalData;
    // if (typeof analyticsData === 'object') {
    //   analyticsData = JSON.stringify(analyticsData)
    //   analyticsData = analyticsData.replaceAll('\\"', "");
    //   analyticsData = JSON.parse(analyticsData)

    // }
    // this.analyticsItem = {} as FORM_EVE_ANALYTICS_ITEM;
    // this.analyticsItem.FORM_NAME = this.formDescription,
    //   this.analyticsItem.FLD_NAME = data.fieldName,
    //   this.analyticsItem.EVENT = data.event,
    //   this.analyticsItem.SUBMISSION_ID = this.taskSubmission.SUBMISSION_ID,
    //   this.analyticsItem.READ_TIMESTAMP = data.timestamp,
    //   this.analyticsItem.SEQ_NO = data.sequenceNumber,
    //   // this.analyticsItem.ADD_DATA = data.additionalData,
    //   this.analyticsItem.ADD_DATA = analyticsData,
    //   this.analyticsItem.FID = this.analyticHeader.LID ? this.analyticHeader.LID : "";
    // if (this.analyticsItem.SEQ_NO === 1) {
    //   this.UID = this.utilityFunction.generateUUID();
    // }
    // let eveItemRes = await this.unviredSDK.dbInsert(this.constants.FORM_EVE_ANALYTICS_ITEM_TABLE, this.analyticsItem, false);
    // if (eveItemRes.type == ResultType.success) {
    //   this.unviredSDK.logInfo('FormRendererPage', 'loadFormAnalystData()', `Info: - Form event analytics item inserted successfully  ${eveItemRes.message}`);
    // } else {
    //   this.unviredSDK.logError('FormRendererPage', 'loadFormAnalystData()', `Error: ${eveItemRes.error}`);
    // }
  }

  // check for previous data present in db and delete
  async insertFormEveAnalyst(data: any) {
    // let eveAnalytic = await this.getDataFromEveAnalyst();
    // let item = await this.getDataFromAnalystItem();
    // if (item.length > 0) {
    //   this.deleteEveAnalyst();
    // }
    // if (eveAnalytic.length > 0) {
    //   this.analyticHeader = eveAnalytic[0];
    // } else {
    //   await this.insertFormEveAnalyicsHeader();
    // }
    // if (data) {
    //   this.loadFormAnalystData(data);
    // }
  }

  async insertFormEveAnalyicsHeader() {
    // let eveanalyticHeader = {} as FORM_EVE_ANALYTICS_HEADER;
    // eveanalyticHeader.PKUID = this.utilityFunction.generateUUID();
    // let insertAnalyticHeader = await this.unviredSDK.dbInsert(this.constants.FORM_EVE_ANALYTICS_TABLE, eveanalyticHeader, true);
    // if (insertAnalyticHeader.type == ResultType.success) {
    //   this.unviredSDK.logInfo('FormRendererPage', 'insertFormEveAnalyicsHeader()', `Info: - Form event analytics header inserted successfully  ${insertAnalyticHeader.message}`);
    // } else {
    //   this.unviredSDK.logError('FormRendererPage', 'insertFormEveAnalyicsHeader()', `Error: ${insertAnalyticHeader.error}`);
    // }
    // let eveHeaderData = await this.getDataFromEveAnalyst();
    // this.analyticHeader = eveHeaderData[0];
  }

  // Make a api call of all the events on navigating from a form
  async sendAnalystDataToServer() {
    let sendAnalyticHeaderToServer: any = {};
    let formAnalyticHeader = {} as FORM_EVE_ANALYTICS_HEADER;
    let formAnalyticItem = {} as FORM_EVE_ANALYTICS_ITEM
    let serverResponseForAnalytic = {} as SyncResult;
    // Get already stored Data from Db and send all stored events to server before navigating back.
    // formAnalyticHeader = await this.getDataFromEveAnalyst();
    // formAnalyticItem = await this.getDataFromAnalystItem();
    // sendAnalyticHeaderToServer[this.constants.FORM_EVE_ANALYTICS_BE] = [{ 'FORM_EVE_ANALYTICS_HEADER': formAnalyticHeader[0], 'FORM_EVE_ANALYTICS_ITEM': formAnalyticItem }];
    // if (!this.isHybridNative) {
    //   serverResponseForAnalytic = await this.unviredSDK.syncForeground(RequestType.REQ, '', sendAnalyticHeaderToServer, this.constants.PA_SAVE_FORM_ANALYTICS, false);
    // } else {
    //   serverResponseForAnalytic = await this.unviredSDK.syncBackground(RequestType.REQ, '', sendAnalyticHeaderToServer, this.constants.PA_SAVE_FORM_ANALYTICS, '', '', true);
    //   // Once Data sent to sever check for outbox sent item and delete db
    //   let outboxObj = await this.unviredSDK.isInOutBox(this.analyticHeader.LID);
    //   if (outboxObj.type == 0) {
    //     if (outboxObj.data == true) {
    //       this.deleteEveAnalyst();
    //     }
    //   }
    // }
  }

  async getDataFromEveAnalyst() {
    // let analyst = await this.unviredSDK.dbSelect(this.constants.FORM_EVE_ANALYTICS_TABLE, '');
    // if (analyst.type == ResultType.success) {
    //   this.unviredSDK.logInfo('FormRendererPage', 'getDataFromEveAnalyst()', `Info: - get data from analytic header   ${analyst.message}`);
    //   return analyst.data;
    // } else {
    //   this.unviredSDK.logError('FormRendererPage', 'getDataFromEveAnalyst()', `Error: ${analyst.error}`);
    //   return [];
    // }
  }

  async getDataFromAnalystItem() {
    // let item = await this.unviredSDK.dbSelect(this.constants.FORM_EVE_ANALYTICS_ITEM_TABLE, '');
    // if (item.type == ResultType.success) {
    //   this.unviredSDK.logInfo('FormRendererPage', 'getDataFromAnalystItem()', `Info: - get data from analytic item table   ${item.message}`);
    //   let analyticsItem = item.data;
    //   let flag = false;
    //   for (let i = 0; i < analyticsItem.length; i++) {
    //     if (analyticsItem[i].SEQ_NO === 1) {
    //       flag = true;
    //       break;
    //     }
    //   }

    //   for (let i = 0; i < analyticsItem.length; i++) {
    //     if (flag) {
    //       analyticsItem[i].SESSION_ID = this.UID;
    //     }
    //   }
    //   if (!flag && analyticsItem.length === 1 && analyticsItem[0].SEQ_NO === null) {
    //     analyticsItem[0].SESSION_ID = this.utilityFunction.generateUUID();
    //   }
    //   return analyticsItem;
    // } else {
    //   this.unviredSDK.logError('FormRendererPage', 'getDataFromAnalystItem()', `Error: ${item.error}`);
    //   return [];
    // }
  }

  async deleteEveAnalyst() {
    // let delDb = await this.unviredSDK.dbDelete(this.constants.FORM_EVE_ANALYTICS_ITEM_TABLE, '');
    // if (delDb.type == ResultType.success) {
    //   this.unviredSDK.logInfo('FormRendererPage', 'deleteEveAnalyst()', `Info: - delete data from analytic item table   ${delDb.message}`);
    //   return delDb;
    // } else {
    //   this.unviredSDK.logError('FormRendererPage', 'deleteEveAnalyst()', `Error: ${delDb.error}`);
    //   return [];
    // }
  }

  // Dismissing bootstrap alerts 
  dismissAlert() {
    this.showUnreadAlerts = false;
  }

  dismissReview() {
    this.showReviewComments = false;
  }

  submitFormViaActionButtonListener(e: any) {
    that.isSubmitFormViaActionButton = true;
    that.saveForm();
  }

  // async printPerview() {
  //   let isAvailable = await this.printer.isAvailable();
  //   let printContent = document.getElementById('render-content');
  //   if (isAvailable && printContent) {
  //     let pickPrinter = await this.printer.pick();
  //     let options: PrintOptions = {
  //       autoFit: false,
  //       margin: false,
  //       monochrome: true,
  //       photo: true,
  //       ui: {
  //         hidePaperFormat: 'true',
  //         height: 400,
  //         width: 100
  //       },
  //       maxWidth: '210mm',
  //       maxHeight: '297mm',
  //       paper: {
  //         name: "IsoA4",
  //         width: '210mm',
  //         height: '297mm',
  //         length: '210 x 297 mm',
  //       },
  //     }
  //     let printData = `<!DOCTYPE html>
  //     <html>
  //     <head>
  //     <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
  //     <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
  //     <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
  //     <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
  //     <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
  //     </head>
  //     <body>
  //     <div>${printContent.innerHTML}</div>
  //     </body>
  //     </html>`;
  //     setTimeout(async () => {
  //       let printView = await this.printer.print(printData, options);
  //     }, 500);
  //   } else {
  //     let printWindow = window.open();
  //     if (printContent) {
  //       printWindow.document.write(`<!DOCTYPE html>
  //       <html><head><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
  //       <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
  //       <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
  //       </head><body>
  //       <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js" integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN" crossorigin="anonymous"></script>
  //       <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
  //       <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
  //       <div>${printContent.innerHTML}</div></body></html>`);
  //       setTimeout(() => {
  //         printWindow.document.close();
  //         printWindow.focus();
  //         printWindow.print();
  //         printWindow.close();
  //       }, 100);
  //     }
  //   }
  // }
  async confirmationAlertForComplete(msg: string): Promise<boolean> {
    let resolveFunction: (confirm: boolean) => void;
    let promise = new Promise<boolean>(resolve => {
      resolveFunction = resolve;
    });

    const alert = await this.alertController.create({
      header: this.translate.instant('Confirmation'),
      message: msg,
      animated: true,
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant('No'),
          role: 'cancel',
          handler: () => {
            resolveFunction(false);
          }
        }, {
          text: this.translate.instant('Yes'),
          handler: () => {
            resolveFunction(true)
          }
        }
      ]
    });
    if (this.loader.isLoading) {
      this.unviredSDK.logInfo('FormRendererPage', 'confirmationAlertForComplete()', 'Hiding Busy indicator...')
      await this.loader.dismissBusyIndicator();
    }
    await alert.present();
    return promise;
  }
  
  async openImagePreview(imageData){
    // this.imagePreviewModalOpened = true;

  const imageModal = await this.modalController.create({
    component: ImagePreviewPage,
    animated: true,
    showBackdrop: true,
    backdropDismiss: false,
    cssClass: 'image-preview-modal',
    enterAnimation: this.loader.enterAnimation,
    leaveAnimation: this.loader.leaveAnimation,
    componentProps: {imageData: imageData }
  });
  await imageModal.present();
  const { data } = await imageModal.onDidDismiss();
  if(!data){
    this.imagePreviewModalOpened = false;
    this.shareData.setImagePreviewModalOpened(false)
    document.removeEventListener('displaySDCWorkflowError', this.openImagePreviewHandler.bind(this), true)
  }

}
ionViewWillLeave() {
  console.log("formrender - ionViewWillLeave called");
}

}
